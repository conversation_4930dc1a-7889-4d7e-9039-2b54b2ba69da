<script setup lang="ts">
import type { DictItem } from '@/utils/dict-util'
// ant-design-vue type
import type { SelectProps } from 'ant-design-vue'
import type { SizeType } from 'ant-design-vue/es/config-provider'
import type { SelectValue } from 'ant-design-vue/es/select'

import type { DictSelectProps } from './type'

import { dictApi } from '@/api/support/dict-api'
import { onMounted, ref, watch } from 'vue'

const {
  keyCode = undefined,
  value = undefined,
  mode = undefined,
  placeholder = '请选择',
  size = 'default',
  disabled = false,
} = defineProps<DictSelectProps>()

const emit = defineEmits(['update:value', 'change'])
// -------------------------- 查询 字典数据 --------------------------

const dictValueList = ref<DictItem[]>([])
async function queryDict() {
  if (!keyCode)
    return

  try {
    const res = await dictApi.valueList(keyCode)
    if (res?.data) {
      dictValueList.value = res.data as unknown as DictItem[]
    }
  }
  catch (error) {
    console.error('获取字典数据失败:', error)
  }
}

onMounted(queryDict)

// -------------------------- 选中 相关、事件 --------------------------

const selectValue = ref(value)
watch(
  () => value,
  (value) => {
    selectValue.value = value
  },
)

function onChange(value: unknown) {
  if (!value) {
    // 当值为空时，根据不同场景返回不同的默认值
    // 在查询表单中通常需要返回undefined，在表单中通常需要返回空字符串或默认值
    const defaultEmptyValue = mode === 'multiple' || mode === 'tags' ? [] : undefined
    emit('update:value', defaultEmptyValue)
    emit('change', defaultEmptyValue)
    return
  }
  if (Array.isArray(value) && value.length === 0) {
    // 处理清空选择的情况，返回undefined而不是空数组
    const defaultEmptyValue = mode === 'multiple' || mode === 'tags' ? [] : undefined
    emit('update:value', defaultEmptyValue)
    emit('change', defaultEmptyValue)
    return
  }
  if (Array.isArray(value)) {
    emit('update:value', value)
    emit('change', value)
  }
  else {
    emit('update:value', value)
    emit('change', value)
  }
}
</script>

<template>
  <div class="w-48">
    <a-select
      v-model:value="selectValue as SelectValue"
      :placeholder="placeholder"
      :allow-clear="true"
      :size="size as SizeType"
      :mode="mode as SelectProps['mode']"
      :disabled="disabled"
      @change="onChange"
    >
      <a-select-option v-for="item in dictValueList" :key="item.valueCode" :value="item.valueCode">
        {{ item.valueName }}
      </a-select-option>
    </a-select>
  </div>
</template>
