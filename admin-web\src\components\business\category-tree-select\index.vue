<!--
  *  目录 树形选择组件
-->
<script setup lang="ts">
import { categoryApi } from '@/api/business/category/category-api'
import { smartSentry } from '@/lib/smart-sentry'
import { onMounted, ref, watch } from 'vue'

const props = defineProps({
  value: Number,
  placeholder: {
    type: String,
    default: '请选择',
  },
  categoryType: String,
  width: {
    type: String,
    default: '100%',
  },
})

const emit = defineEmits(['update:value', 'change'])

// -----------------  查询 目录 数据 -----------------
const categoryTreeData = ref([])
async function queryCategoryTree() {
  if (!props.categoryType) {
    categoryTreeData.value = []
    return
  }
  try {
    const param = {
      categoryType: props.categoryType,
    }
    const resp = await categoryApi.queryCategoryTree(param)
    categoryTreeData.value = resp.data
  }
  catch (e) {
    smartSentry.captureError(e)
  }
}

// -----------------  选中相关监听、事件 -----------------
const selectValue = ref(props.value)
// 箭头value变化
watch(
  () => props.value,
  (newValue) => {
    selectValue.value = newValue
  },
)

// 监听类型变化
watch(
  () => props.categoryType,
  () => {
    queryCategoryTree()
  },
)

function onChange(value: number) {
  emit('update:value', value)
  emit('change', value)
}

onMounted(queryCategoryTree)
</script>

<template>
  <a-tree-select
    v-model:value="selectValue"
    :style="`width:${width}`"
    :dropdown-style="{ maxHeight: '400px', overflowX: 'auto' }"
    :tree-data="categoryTreeData"
    :placeholder="placeholder"
    :allow-clear="true"
    tree-default-expand-all
    @change="onChange"
  />
</template>
