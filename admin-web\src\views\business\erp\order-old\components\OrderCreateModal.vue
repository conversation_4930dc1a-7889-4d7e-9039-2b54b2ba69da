<script setup lang="ts">
import type { OrderService } from '../service/OrderService'
import { message } from 'ant-design-vue'
import { inject, ref } from 'vue'
import { ButtonGroup } from '@/components/framework-new/Button'
import { Card } from '@/components/framework-new/Card'
import {
  CustomIconButton as IconButton,
} from '@/components/framework-new/CustomIcon'
import { FlexRow } from '@/components/framework-new/Flex'
import { InputNumber, InputText } from '@/components/framework-new/Input'
import { Modal } from '@/components/framework-new/Modal'
import Pagination from '@/components/framework/ax-pagination/index.vue'
import Table from '@/components/framework/ax-table/index.vue'
import { CRUD_KEY } from '@/service/BaseCrudService'

// 注入订单服务
const orderService = inject<OrderService>(CRUD_KEY)!

// 商品列表表格列配置
const productColumns = [
  {
    title: '商品名称',
    dataIndex: 'name',
    key: 'name',
    width: 200,
  },
  {
    title: '商品编码',
    dataIndex: 'productCode',
    key: 'productCode',
    width: 120,
  },
  {
    title: '单位',
    dataIndex: 'unit',
    key: 'unit',
    width: 80,
  },
  {
    title: '分类',
    dataIndex: 'categoryName',
    key: 'categoryName',
    width: 100,
  },
  {
    title: '操作',
    key: 'action',
    width: 80,
  },
]

// 已选商品表格列配置
const selectedColumns = [
  {
    title: '商品名称',
    dataIndex: 'name',
    key: 'name',
    width: 150,
  },
  {
    title: 'SKU编号',
    dataIndex: 'productCode',
    key: 'productCode',
    width: 100,
  },
  {
    title: '数量',
    key: 'quantity',
    width: 100,
  },
  {
    title: '单价',
    key: 'unitPrice',
    width: 80,
  },
  {
    title: '小计',
    key: 'subtotal',
    width: 80,
  },
  {
    title: '操作',
    key: 'action',
    width: 60,
  },
]

// 搜索商品
function handleSearch() {
  orderService.searchProducts()
}

// 搜索框回车事件
function handleSearchEnter() {
  handleSearch()
}

// 添加商品
function handleAddProduct(product: any) {
  orderService.addProduct(product)
}

// 移除商品
function handleRemoveProduct(index: number) {
  orderService.removeProduct(index)
}

// 更新商品数量
function handleQuantityChange(index: number, quantity: number) {
  orderService.updateProductQuantity(index, quantity)
}

// 更新商品单价
function handlePriceChange(index: number, unitPrice: number) {
  orderService.updateProductPrice(index, unitPrice)
}

// 分页变化
function handlePageChange(page: number, pageSize?: number) {
  orderService.onProductPageChange(page, pageSize)
}

// 提交订单
async function handleSubmit() {
  try {
    await orderService.submitOrder()
    message.success('订单创建成功')
  }
  catch (error) {
    message.error(error instanceof Error ? error.message : '订单创建失败')
  }
}

// 取消
function handleCancel() {
  orderService.closeCreateModal()
}
</script>

<template>
  <Modal
    :open="orderService.createModalOpen"
    title="新建订单"
    width="80vw"
    :style="{ height: '70vh' }"
    :mask-closable="false"
    :destroy-on-close="true"
    @cancel="handleCancel"
  >
    <div class="order-create-modal">
      <FlexRow class="modal-content" :gap="16">
        <!-- 左侧：商品选择区域 (60%) -->
        <div class="product-selection" style="flex: 6;">
          <Card title="商品选择" size="small">
            <!-- 搜索栏 -->
            <div class="search-bar">
              <FlexRow :gap="8">
                <InputText
                  v-model:value="orderService.productSearchParam.name"
                  placeholder="搜索商品名称/SKU编号"
                  style="width: 300px"
                  @press-enter="handleSearchEnter"
                />
                <IconButton
                  label="搜索"
                  icon-type="SearchOutlined"
                  type="primary"
                  @click="handleSearch"
                />
              </FlexRow>
            </div>

            <!-- 商品列表 -->
            <Table
              :columns="productColumns"
              :data-source="orderService.productList"
              :loading="orderService.productLoading"
              :pagination="false"
              :scroll="{ y: 300 }"
              size="small"
              row-key="id"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'action'">
                  <IconButton
                    label="添加"
                    icon-type="PlusOutlined"
                    size="small"
                    @click="handleAddProduct(record)"
                  />
                </template>
              </template>
            </Table>

            <!-- 分页 -->
            <div class="pagination-wrapper">
              <Pagination
                :current="orderService.productSearchParam.pageParam.pageNum"
                :page-size="orderService.productSearchParam.pageParam.pageSize"
                :total="orderService.productTotal"
                :show-size-changer="true"
                :show-quick-jumper="true"
                :show-total="(total) => `共 ${total} 条`"
                @change="handlePageChange"
              />
            </div>
          </Card>
        </div>

        <!-- 右侧：已选商品区域 (40%) -->
        <div class="selected-products" style="flex: 4;">
          <Card title="已选商品" size="small">
            <!-- 已选商品列表 -->
            <Table
              :columns="selectedColumns"
              :data-source="orderService.selectedProducts"
              :pagination="false"
              :scroll="{ y: 300 }"
              size="small"
              row-key="id"
            >
              <template #bodyCell="{ column, record, index }">
                <template v-if="column.key === 'quantity'">
                  <InputNumber
                    :value="record.quantity"
                    :min="1"
                    :max="999"
                    size="small"
                    style="width: 80px"
                    @change="(value) => handleQuantityChange(index, value)"
                  />
                </template>
                <template v-else-if="column.key === 'unitPrice'">
                  <InputNumber
                    :value="record.unitPrice"
                    :min="0"
                    :precision="2"
                    size="small"
                    style="width: 80px"
                    @change="(value) => handlePriceChange(index, value)"
                  />
                </template>
                <template v-else-if="column.key === 'subtotal'">
                  ¥{{ record.subtotal.toFixed(2) }}
                </template>
                <template v-else-if="column.key === 'action'">
                  <IconButton
                    icon-type="DeleteOutlined"
                    size="small"
                    danger
                    @click="handleRemoveProduct(index)"
                  />
                </template>
              </template>
            </Table>

            <!-- 总金额 -->
            <div class="total-amount">
              <strong>总金额: ¥{{ orderService.totalAmount.toFixed(2) }}</strong>
            </div>
          </Card>
        </div>
      </FlexRow>
    </div>

    <template #footer>
      <ButtonGroup>
        <IconButton label="取消" @click="handleCancel" />
        <IconButton
          label="确认开单"
          type="primary"
          icon-type="CheckOutlined"
          :disabled="orderService.selectedProducts.length === 0"
          @click="handleSubmit"
        />
      </ButtonGroup>
    </template>
  </Modal>
</template>

<style scoped>
.order-create-modal {
  height: 60vh;
  overflow: hidden;
}

.modal-content {
  height: 100%;
}

.product-selection,
.selected-products {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.search-bar {
  margin-bottom: 16px;
}

.pagination-wrapper {
  margin-top: 16px;
  text-align: center;
}

.total-amount {
  margin-top: 16px;
  padding: 12px;
  text-align: right;
  border-top: 2px solid #1890ff;
  background-color: #f0f9ff;
  border-radius: 4px;
}

:deep(.ant-card-body) {
  height: calc(100% - 57px);
  display: flex;
  flex-direction: column;
}

:deep(.ant-table-wrapper) {
  flex: 1;
  overflow: hidden;
}
</style>
