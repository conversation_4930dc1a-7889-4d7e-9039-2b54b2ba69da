import { inject, provide, ref } from 'vue'

// BoxSelect服务接口 - 只包含业务相关的属性和方法
export interface SelectServiceInterface<T> {
  // 对外暴露的数据（直接返回值而不是ComputedRef）
  selectedValue: T | undefined
  Config: T
  onChange: () => void
}

// 将 SELECT_KEY 定义移到类外部并导出，并添加类型提示
export const SELECT_KEY = Symbol('selectService')

/**
 * 盒子选择器服务类
 * 可以直接实例化使用，也可以通过内部单例使用
 */
export class SelectService<T> implements SelectServiceInterface<T> {
  // 私有属性 - 内部使用的ref
  private _selectedValue = ref<T | undefined>(undefined)

  // 通过getter访问器暴露属性，更优雅
  get selectedValue(): T | undefined {
    return this._selectedValue.value
  }

  // 通过setter访问器设置属性，与getter成对出现
  set selectedValue(value: T | undefined) {
    this._selectedValue.value = value
  }

  get Config(): T {
    return this._selectedValue.value || { }
  }

  /**
   * 选项变化时的回调钩子，子类可重写以添加逻辑
   */
  onChange(): void {
  }

  /**
   * 将当前服务实例提供给依赖注入系统
   */
  provide(): void {
    provide(SELECT_KEY, this)
  }
}

/**
 * 注入盒子选择器服务
 * @param key 注入键，默认使用BOX_SELECT_KEY
 * @returns 注入的服务实例
 */
export function injectSelectService<T>(key: typeof SELECT_KEY = SELECT_KEY): SelectServiceInterface<T> {
  const service = inject(key) as SelectServiceInterface<T>
  if (!service) {
    throw new Error('SelectService is not provided. Make sure to call service.provide() in a parent component.')
  }

  return service
}
