<script lang="ts" setup>
import type { OrderService } from '../service/OrderService'
import type { ProductResult } from '@/api/business/product/model/product-form-model'
import { computed, inject, ref } from 'vue'
import { productApi } from '@/api/business/product/product-api'
import { ButtonGroup } from '@/components/framework-new/Button'
import {
  CustomIconButton as IconButton,
} from '@/components/framework-new/CustomIcon'
import { FlexRow } from '@/components/framework-new/Flex'
import { Form, FormItem, FormRow } from '@/components/framework-new/Form'
import { InputNumber, InputText } from '@/components/framework-new/Input'
import { Modal } from '@/components/framework-new/Modal'
import { DictSelect } from '@/components/support-new/Dict'
import { CRUD_KEY } from '@/service/BaseCrudService'

// 注入订单服务
const orderService = inject<OrderService>(CRUD_KEY)!

// 商品列表
const productList = ref<ProductResult[]>([])
const selectedProducts = ref<Array<ProductResult & { quantity: number, price: number }>>([])
const searchKeyword = ref('')

// 订单基本信息
const orderForm = ref({
  customerName: '',
  orderType: 'SALES',
  orderDate: '',
  deliveryDate: '',
  contactName: '',
  contactPhone: '',
  remark: '',
})

// 获取商品列表
async function getProductList() {
  try {
    const response = await productApi.productList({
      name: searchKeyword.value,
      status: '1', // 只获取上架商品
    })
    if (response.data) {
      productList.value = response.data
    }
  }
  catch (error) {
    console.error('获取商品列表失败:', error)
  }
}

// 搜索商品
function searchProducts() {
  getProductList()
}

// 添加商品到订单
function addProduct(product: ProductResult) {
  const existingIndex = selectedProducts.value.findIndex(p => p.id === product.id)
  if (existingIndex === -1) {
    selectedProducts.value.push({
      ...product,
      quantity: 1,
      price: 0,
    })
  }
  else {
    selectedProducts.value[existingIndex].quantity += 1
  }
}

// 移除商品
function removeProduct(index: number) {
  selectedProducts.value.splice(index, 1)
}

// 计算总金额
const totalAmount = computed(() => {
  return selectedProducts.value.reduce((sum, item) => {
    return sum + (item.quantity * item.price)
  }, 0)
})

// 打开弹窗
function open() {
  orderService.openCreateOrder()
  getProductList()
  // 设置默认订单日期为今天
  orderForm.value.orderDate = new Date().toISOString().split('T')[0]
}

// 关闭弹窗
function close() {
  orderService.closeCreateOrder()
  // 重置表单
  orderForm.value = {
    customerName: '',
    orderType: 'SALES',
    orderDate: '',
    deliveryDate: '',
    contactName: '',
    contactPhone: '',
    remark: '',
  }
  selectedProducts.value = []
  searchKeyword.value = ''
  productList.value = []
}

// 创建订单
async function createOrder() {
  if (!orderForm.value.customerName) {
    return
  }
  if (selectedProducts.value.length === 0) {
    return
  }

  try {
    const orderData = {
      ...orderForm.value,
      subtotalAmount: totalAmount.value,
      finalAmount: totalAmount.value,
    }

    // 设置表单数据
    Object.assign(orderService.formData, orderData)
    // 提交表单
    const success = await orderService.submitForm()
    if (success) {
      close()
      orderService.queryPage()
    }
  }
  catch (error) {
    console.error('创建订单失败:', error)
  }
}

// 暴露方法
defineExpose({
  open,
  close,
})
</script>

<template>
  <Modal
    :open="orderService.createOrderOpen"
    title="开单"
    :width="1200"
    @cancel="close"
  >
    <div class="order-create-container">
      <!-- 订单基本信息 -->
      <div class="order-info">
        <h3>订单信息</h3>
        <Form>
          <FormRow>
            <FormItem label="客户名称" required>
              <InputText v-model:value="orderForm.customerName" placeholder="请输入客户名称" />
            </FormItem>
            <FormItem label="开单方式">
              <DictSelect v-model:value="orderForm.orderType" key-code="order_type" />
            </FormItem>
            <FormItem label="订单日期">
              <InputText v-model:value="orderForm.orderDate" type="date" />
            </FormItem>
          </FormRow>
          <FormRow>
            <FormItem label="交货日期">
              <InputText v-model:value="orderForm.deliveryDate" type="date" />
            </FormItem>
            <FormItem label="联系人">
              <InputText v-model:value="orderForm.contactName" placeholder="请输入联系人" />
            </FormItem>
            <FormItem label="联系电话">
              <InputText v-model:value="orderForm.contactPhone" placeholder="请输入联系电话" />
            </FormItem>
          </FormRow>
          <FormRow>
            <FormItem label="备注">
              <InputText v-model:value="orderForm.remark" placeholder="请输入备注" />
            </FormItem>
          </FormRow>
        </Form>
      </div>

      <!-- 商品选择区域 -->
      <div class="product-selection">
        <FlexRow>
          <!-- 左侧：商品列表 -->
          <div class="product-list">
            <h3>商品列表</h3>
            <FlexRow>
              <InputText
                v-model:value="searchKeyword"
                placeholder="搜索商品名称"
                style="width: 200px"
                @press-enter="searchProducts"
              />
              <IconButton label="搜索" icon-type="SearchOutlined" @click="searchProducts" />
            </FlexRow>
            <div class="product-items">
              <div
                v-for="product in productList"
                :key="product.id"
                class="product-item"
                @click="addProduct(product)"
              >
                <div class="product-name">
                  {{ product.name }}
                </div>
                <div class="product-code">
                  编码: {{ product.productCode }}
                </div>
                <div class="product-unit">
                  单位: {{ product.unit }}
                </div>
              </div>
            </div>
          </div>

          <!-- 右侧：已选商品 -->
          <div class="selected-products">
            <h3>已选商品</h3>
            <div class="selected-items">
              <div
                v-for="(item, index) in selectedProducts"
                :key="item.id"
                class="selected-item"
              >
                <div class="item-info">
                  <div class="item-name">
                    {{ item.name }}
                  </div>
                  <div class="item-code">
                    {{ item.productCode }}
                  </div>
                </div>
                <div class="item-quantity">
                  数量: <InputNumber v-model:value="item.quantity" :min="1" style="width: 80px" />
                </div>
                <div class="item-price">
                  单价: <InputNumber v-model:value="item.price" :min="0" :precision="2" style="width: 100px" />
                </div>
                <div class="item-total">
                  小计: {{ (item.quantity * item.price).toFixed(2) }}
                </div>
                <IconButton
                  icon-type="DeleteOutlined"
                  type="text"
                  danger
                  @click="removeProduct(index)"
                />
              </div>
            </div>

            <!-- 总计 -->
            <div class="total-amount">
              <strong>总金额: ¥{{ totalAmount.toFixed(2) }}</strong>
            </div>
          </div>
        </FlexRow>
      </div>
    </div>

    <template #footer>
      <ButtonGroup>
        <IconButton label="取消" @click="close" />
        <IconButton
          label="创建订单"
          type="primary"
          icon-type="CheckOutlined"
          @click="createOrder"
        />
      </ButtonGroup>
    </template>
  </Modal>
</template>

<style scoped>
.order-create-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.order-info {
  padding: 16px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  background-color: #fafafa;
}

.product-selection {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.product-selection > div {
  display: flex;
  gap: 20px;
  min-height: 400px;
}

.product-list {
  flex: 1;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  padding: 16px;
}

.product-items {
  margin-top: 16px;
  max-height: 350px;
  overflow-y: auto;
}

.product-item {
  padding: 12px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.product-item:hover {
  border-color: #1890ff;
  background-color: #f0f9ff;
}

.product-name {
  font-weight: bold;
  margin-bottom: 4px;
}

.product-code,
.product-unit {
  font-size: 12px;
  color: #666;
}

.selected-products {
  flex: 1;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  padding: 16px;
}

.selected-items {
  margin-top: 16px;
  max-height: 300px;
  overflow-y: auto;
}

.selected-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  margin-bottom: 8px;
  background-color: #f9f9f9;
}

.item-info {
  flex: 1;
  min-width: 120px;
}

.item-name {
  font-weight: bold;
  margin-bottom: 4px;
}

.item-code {
  font-size: 12px;
  color: #666;
}

.item-quantity,
.item-price,
.item-total {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.total-amount {
  margin-top: 16px;
  padding: 12px;
  text-align: right;
  border-top: 2px solid #1890ff;
  background-color: #f0f9ff;
  border-radius: 4px;
}
</style>
