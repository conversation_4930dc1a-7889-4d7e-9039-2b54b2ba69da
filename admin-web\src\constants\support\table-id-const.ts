/*
 * @Description: 表格id
 * @LastEditTime: 2022-08-21
 * @LastEditors: zhuoda
 */

// system系统功能表格初始化id
const systemInitTableId = 10000

// support支撑功能表格初始化id
const supportInitTableId = 20000

// 业务表格初始化id
const businessOAInitTableId = 30000

const businessERPInitTableId = 40000

export const TABLE_ID_CONST = {
  /**
   * 业务
   */
  BUSINESS: {
    OA: {
      NOTICE: businessOAInitTableId + 1, // 通知公告
      ENTERPRISE: businessOAInitTableId + 2, // 企业信息
      ENTERPRISE_EMPLOYEE: businessOAInitTableId + 3, // 企业员工
      ENTERPRISE_BANK: businessOAInitTableId + 4, // 企业银行
      ENTERPRISE_INVOICE: businessOAInitTableId + 5, // 企业发票
    },
    ERP: {
      GOODS: businessERPInitTableId + 1, // 商品管理
      CUSTOMER: businessERPInitTableId + 2, // 客户管理
      PAPER: businessERPInitTableId + 3, // 纸张管理;
      SUPPLIER: businessERPInitTableId + 4, // 供应商管理
      PAPER_QUOTATION: businessERPInitTableId + 5, // 纸张报价管理
      BOX_QUOTATION: businessERPInitTableId + 6, // 纸箱报价管理
      ENTITY_PARAM: businessERPInitTableId + 7, // 实体参数管理
      ENTITY_PARAM_TYPE: businessERPInitTableId + 8, // 实体参数类型管理
      ORDER: businessERPInitTableId + 9, // 订单管理
      INVENTORY: businessERPInitTableId + 10, // 库存管理
      WAREHOUSE: businessERPInitTableId + 11, // 仓库管理
    },
    EQUIPMENT: {
      EQUIPMENT_DEMO: 'BUSINESS.EQUIPMENT.EQUIPMENT_DEMO',
    },
  },

  /**
   * 系统
   */
  SYSTEM: {
    EMPLOYEE: systemInitTableId + 1, // 员工
    MENU: systemInitTableId + 2, // 菜单
    POSITION: systemInitTableId + 3, // 职位
  },
  /**
   * 支撑
   */
  SUPPORT: {
    CONFIG: supportInitTableId + 1, // 参数配置
    DICT: supportInitTableId + 2, // 字典
    SERIAL_NUMBER: supportInitTableId + 3, // 单号
    OPERATE_LOG: supportInitTableId + 4, // 请求监控
    HEART_BEAT: supportInitTableId + 5, // 心跳
    LOGIN_LOG: supportInitTableId + 6, // 登录日志
    RELOAD: supportInitTableId + 7, // reload
    HELP_DOC: supportInitTableId + 8, // 帮助文档
    JOB: supportInitTableId + 9, // Job
    JOB_LOG: supportInitTableId + 10, // JobLog
  },
}
