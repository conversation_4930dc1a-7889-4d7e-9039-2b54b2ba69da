<script lang="ts" setup>
import { CRUD_KEY } from '@/service/BaseCrudService'
import { CustomIconButton as IconButton } from '@/components/framework-new/CustomIcon'
import { computed } from 'vue'
import type { OperateParams, OperateService } from './type'

// 组件属性
const {
    addConfig,
    batchDeleteConfig,
    importConfig,
    exportConfig,
} = defineProps<OperateParams>()

// 通过依赖注入获取服务实例
const service = inject<OperateService>(CRUD_KEY)!
if (!service) {
  throw new Error('OperateService is not injected')
}

// 计算属性 - 各按钮是否可见
const showAdd = computed(() => !!addConfig)
const showBatchDelete = computed(() => !!batchDeleteConfig)
const showImport = computed(() => !!importConfig)
const showExport = computed(() => !!exportConfig)

// 计算批量删除按钮是否禁用
const disableBatchDelete = computed(() => {
  // 安全地检查selectedRowKeyList是否存在且为空
  return !service.selectedRowKeyList?.length
})
</script>

<template>
  <!-- 新增按钮 -->
    <IconButton v-if="showAdd" v-privilege="addConfig" type="primary" icon-type="PlusOutlined" label="新增" @click="() => service.openAddForm()" />

  <!-- 批量删除按钮 -->
  <IconButton v-if="showBatchDelete" v-privilege="batchDeleteConfig" danger icon-type="DeleteOutlined" label="批量删除" :disabled="disableBatchDelete" @click="() => service.confirmBatchDeleteEntities()" />

  <!-- 导入按钮 -->
  <IconButton v-if="showImport" v-privilege="importConfig" icon-type="ImportOutlined" label="导入" @click="() => service.showImportModal()" />

  <!-- 导出按钮 -->
  <IconButton v-if="showExport" v-privilege="exportConfig" icon-type="ExportOutlined" label="导出" @click="() => service.onExport()" />
</template>