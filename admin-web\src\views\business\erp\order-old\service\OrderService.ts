import type { OrderFormParam, OrderPageParam, OrderResult } from '@/api/business/order/model/order-form-model'
import type { ProductResult } from '@/api/business/product/model/product-form-model'
import { computed, reactive, ref } from 'vue'
import { orderApi } from '@/api/business/order/order-api'
import { productApi } from '@/api/business/product/product-api'
import { BaseCrudService } from '@/service/BaseCrudService'
import { orderColumns } from '../config/columns'

// 已选商品类型
export interface SelectedProduct extends ProductResult {
  quantity: number
  unitPrice: number
  subtotal: number
}

/**
 * 订单服务
 * 提供订单相关的业务逻辑和数据管理
 */
export class OrderService extends BaseCrudService<OrderResult, OrderFormParam, OrderPageParam> {
  // ==================== 开单弹窗状态管理 ====================

  // 开单弹窗显示状态
  private _createModalOpen = ref(false)

  // 商品列表数据
  private _productList = ref<ProductResult[]>([])

  // 商品搜索参数
  private _productSearchParam = reactive({
    name: '',
    status: '1', // 只获取上架商品
    pageParam: {
      pageNum: 1,
      pageSize: 10,
    },
  })

  // 商品列表总数
  private _productTotal = ref(0)

  // 商品列表加载状态
  private _productLoading = ref(false)

  // 已选商品列表
  private _selectedProducts = ref<SelectedProduct[]>([])

  // ==================== Getters ====================

  get createModalOpen() {
    return this._createModalOpen.value
  }

  get productList() {
    return this._productList.value
  }

  get productSearchParam() {
    return this._productSearchParam
  }

  get productTotal() {
    return this._productTotal.value
  }

  get productLoading() {
    return this._productLoading.value
  }

  get selectedProducts() {
    return this._selectedProducts.value
  }

  // 计算总金额
  get totalAmount() {
    return computed(() => {
      return this._selectedProducts.value.reduce((sum, item) => sum + item.subtotal, 0)
    }).value
  }

  constructor() {
    // 初始化服务
    super(
      '订单', // 业务名称
      orderColumns,
      {
        // 使用已有的API
        add: orderApi.addOrder,
        queryPage: orderApi.orderPage,
        getDetail: orderApi.orderDetail,
        update: orderApi.updateOrder,
        import: orderApi.importOrder,
        export: orderApi.exportOrder,
        delete: orderApi.deleteOrder,
        batchDelete: orderApi.batchDeleteOrder,
        downloadTemplate: orderApi.downloadTemplate,
      },
      // 初始查询参数
      {
        pageParam: {
          pageNum: 1,
          pageSize: 10,
        },
      } as Partial<OrderPageParam>,
    )
  }

  // ==================== 开单弹窗相关方法 ====================

  /**
   * 打开开单弹窗
   */
  openCreateModal = () => {
    this._createModalOpen.value = true
    this.resetCreateModalData()
    this.searchProducts()
  }

  /**
   * 关闭开单弹窗
   */
  closeCreateModal = () => {
    this._createModalOpen.value = false
    this.resetCreateModalData()
  }

  /**
   * 重置开单弹窗数据
   */
  private resetCreateModalData = () => {
    this._selectedProducts.value = []
    this._productSearchParam.name = ''
    this._productSearchParam.pageParam.pageNum = 1
    this._productList.value = []
    this._productTotal.value = 0
  }

  /**
   * 搜索商品
   */
  searchProducts = async () => {
    try {
      this._productLoading.value = true
      const response = await productApi.productPage({
        name: this._productSearchParam.name,
        status: this._productSearchParam.status,
        pageParam: this._productSearchParam.pageParam,
      })

      if (response.data) {
        this._productList.value = response.data.list || []
        this._productTotal.value = response.data.total || 0
      }
    }
    catch (error) {
      console.error('搜索商品失败:', error)
    }
    finally {
      this._productLoading.value = false
    }
  }

  /**
   * 添加商品到已选列表
   */
  addProduct = (product: ProductResult) => {
    const existingIndex = this._selectedProducts.value.findIndex(p => p.id === product.id)

    if (existingIndex >= 0) {
      // 如果商品已存在，增加数量
      this.updateProductQuantity(existingIndex, this._selectedProducts.value[existingIndex].quantity + 1)
    }
    else {
      // 添加新商品
      const selectedProduct: SelectedProduct = {
        ...product,
        quantity: 1,
        unitPrice: 0, // 默认单价为0，需要用户输入
        subtotal: 0,
      }
      this._selectedProducts.value.push(selectedProduct)
    }
  }

  /**
   * 移除已选商品
   */
  removeProduct = (index: number) => {
    this._selectedProducts.value.splice(index, 1)
  }

  /**
   * 更新商品数量
   */
  updateProductQuantity = (index: number, quantity: number) => {
    if (quantity < 1)
      return

    const product = this._selectedProducts.value[index]
    product.quantity = quantity
    product.subtotal = product.quantity * product.unitPrice
  }

  /**
   * 更新商品单价
   */
  updateProductPrice = (index: number, unitPrice: number) => {
    if (unitPrice < 0)
      return

    const product = this._selectedProducts.value[index]
    product.unitPrice = unitPrice
    product.subtotal = product.quantity * product.unitPrice
  }

  /**
   * 商品搜索分页变化
   */
  onProductPageChange = (page: number, pageSize?: number) => {
    this._productSearchParam.pageParam.pageNum = page
    if (pageSize) {
      this._productSearchParam.pageParam.pageSize = pageSize
    }
    this.searchProducts()
  }

  /**
   * 提交订单
   */
  submitOrder = async () => {
    if (this._selectedProducts.value.length === 0) {
      throw new Error('请选择商品')
    }

    // 构建订单数据并设置到表单数据中
    Object.assign(this.formData, {
      // 基础订单信息
      orderNo: '', // 后端生成
      orderType: 'SALES',
      orderStatus: 'PENDING',
      orderDate: new Date().toISOString().split('T')[0],

      // 金额信息
      subtotalAmount: this.totalAmount,
      finalAmount: this.totalAmount,

      // 商品明细
      orderItems: this._selectedProducts.value.map(product => ({
        productId: product.id!,
        productName: product.name,
        productCode: product.productCode,
        quantity: product.quantity,
        unitPrice: product.unitPrice,
        subtotal: product.subtotal,
      })),
    })

    // 提交订单
    const success = await this.submitForm(() => {
      this.closeCreateModal()
      this.queryPage()
    })

    return success
  }
}

// 单例模式
export const orderService = new OrderService()
