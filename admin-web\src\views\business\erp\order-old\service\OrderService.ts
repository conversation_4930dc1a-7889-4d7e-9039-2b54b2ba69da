import type { OrderFormParam, OrderPageParam, OrderResult } from '@/api/business/order/model/order-form-model'
import { ref } from 'vue'
import { orderApi } from '@/api/business/order/order-api'
import { BaseCrudService } from '@/service/BaseCrudService'
import { orderColumns } from '../config/columns'

/**
 * 订单服务
 * 提供订单相关的业务逻辑和数据管理
 */
export class OrderService extends BaseCrudService<OrderResult, OrderFormParam, OrderPageParam> {
  // 开单弹窗状态
  private _createOrderOpen = ref(false)

  // 开单弹窗状态的 getter
  get createOrderOpen() {
    return this._createOrderOpen.value
  }

  constructor() {
    // 初始化服务
    super(
      '订单', // 业务名称
      orderColumns,
      {
        // 使用已有的API
        add: orderApi.addOrder,
        queryPage: orderApi.orderPage,
        getDetail: orderApi.orderDetail,
        update: orderApi.updateOrder,
        import: orderApi.importOrder,
        export: orderApi.exportOrder,
        delete: orderApi.deleteOrder,
        batchDelete: orderApi.batchDeleteOrder,
        downloadTemplate: orderApi.downloadTemplate,
      },
      // 初始查询参数
      {
        pageParam: {
          pageNum: 1,
          pageSize: 10,
        },
      } as Partial<OrderPageParam>,
    )
  }

  /**
   * 打开开单弹窗
   */
  openCreateOrder() {
    this._createOrderOpen.value = true
  }

  /**
   * 关闭开单弹窗
   */
  closeCreateOrder() {
    this._createOrderOpen.value = false
  }
}

// 单例模式
export const orderService = new OrderService()
