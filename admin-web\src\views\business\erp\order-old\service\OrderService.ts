import type { OrderFormParam, OrderPageParam, OrderResult } from '@/api/business/order/model/order-form-model'
import { ref } from 'vue'
import { orderApi } from '@/api/business/order/order-api'
import { BaseCrudService } from '@/service/BaseCrudService'
import { orderColumns } from '../config/columns'

/**
 * 订单服务
 * 提供订单相关的业务逻辑和数据管理
 */
export class OrderService extends BaseCrudService<OrderResult, OrderFormParam, OrderPageParam> {
  // 开单弹窗引用
  private createOrderRef = ref<{ open: () => void }>()

  constructor() {
    // 初始化服务
    super(
      '订单', // 业务名称
      orderColumns,
      {
        // 使用已有的API
        add: orderApi.addOrder,
        queryPage: orderApi.orderPage,
        getDetail: orderApi.orderDetail,
        update: orderApi.updateOrder,
        import: orderApi.importOrder,
        export: orderApi.exportOrder,
        delete: orderApi.deleteOrder,
        batchDelete: orderApi.batchDeleteOrder,
        downloadTemplate: orderApi.downloadTemplate,
      },
      // 初始查询参数
      {
        pageParam: {
          pageNum: 1,
          pageSize: 10,
        },
      } as Partial<OrderPageParam>,
    )
  }

  /**
   * 设置开单弹窗引用
   */
  setCreateOrderRef(ref: { open: () => void }) {
    console.log('设置开单弹窗引用:', ref)
    this.createOrderRef.value = ref
  }

  /**
   * 打开开单弹窗
   */
  openCreateOrder() {
    console.log('尝试打开开单弹窗, createOrderRef:', this.createOrderRef)
    console.log('createOrderRef.value:', this.createOrderRef.value)
    if (!this.createOrderRef.value) {
      console.error('createOrderRef.value 为空，无法打开弹窗')
      return
    }
    this.createOrderRef.value.open()
  }
}

// 单例模式
export const orderService = new OrderService()
