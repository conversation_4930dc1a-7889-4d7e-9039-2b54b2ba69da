<script setup lang="ts">
import type { FormInstance } from 'ant-design-vue/es/form'
import { ref } from 'vue'

const formRef = ref<FormInstance>()

defineExpose({
  validate: (nameList?: string | (string | number)[]) => formRef.value?.validate?.(nameList),
  // 你还可以暴露其他方法，比如 resetFields、clearValidate 等
})
</script>

<template>
  <a-form ref="formRef" v-bind="$attrs" class="smart-query-form">
    <slot />
  </a-form>
</template>
