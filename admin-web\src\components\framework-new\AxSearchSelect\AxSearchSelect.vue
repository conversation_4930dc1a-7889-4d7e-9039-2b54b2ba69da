<script setup lang="ts">
import type { AxSearchSelectService } from './type'
import { inject } from 'vue'
import { SELECT_KEY } from './service'
// import { SELECT_SERVICE_KEY } from './constants'

const service = inject<AxSearchSelectService>(SELECT_KEY)
</script>

<template>
  <a-select
    :value="service?.selectedValue?.value || undefined"
    :loading="service?.loading?.value || false"
    :disabled="service?.config?.disabled || false"
    :placeholder="service?.config?.placeholder || ''"
    :allow-clear="service?.config?.allowClear || false"
    :show-search="service?.config?.showSearch || false"
    @change="service?.onSelectChange"
    @focus="service?.onFocus"
    @blur="service?.onBlur"
  >
    <a-select-option
      v-for="item in service?.options?.value"
      :key="service?.getOptionValue(item)"
      :value="service?.getOptionValue(item)"
    >
      <slot name="option" :item="item">
        {{ service?.getOptionLabel(item) }}
      </slot>
    </a-select-option>

    <template #notFoundContent>
      <slot name="notFoundContent">
        <span v-if="service?.loading?.value">加载中...</span>
        <span v-else>暂无数据</span>
      </slot>
    </template>
  </a-select>
</template>
