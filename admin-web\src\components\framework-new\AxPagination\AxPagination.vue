<script lang="ts" setup>
import { PAGE_SIZE_OPTIONS } from '@/constants/common-const'
import { CRUD_KEY } from '@/service/BaseCrudService'
import type { PaginationComponentService } from './type'

const service = inject<PaginationComponentService>(CRUD_KEY)!
if (!service) {
  throw new Error('PaginationComponentService is not injected')
}
</script>

<template>
  <div class="ax-pagination">
    <a-pagination
      :current="service.pageParam.pageNum"
      :page-size="service.pageParam.pageSize"
      :total="service.total"
      :page-size-options="PAGE_SIZE_OPTIONS"
      :show-quick-jumper="true"
      :show-size-changer="true"
      :show-less-items="true"
      :show-total="(total) => `共${total}条`"
      @change="service.onPageChange"
    />
  </div>
</template>

<style scoped>
.ax-pagination {
  padding: 16px 0;
  text-align: right;
}
</style>
