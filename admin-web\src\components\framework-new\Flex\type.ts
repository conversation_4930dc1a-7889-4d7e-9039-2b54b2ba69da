export type FlexAlign = 'center' | 'start' | 'end' | 'between' | 'around' | 'evenly'

export interface FrameworkFlexRowProps {
  /**
   * 间距设置，支持 unocss gap 语法
   * @default '0px'
   * @example '4px', '16px', '4', '1rem'
   */
  gap?: string

  /**
   * flex 容器内容分布方式（justify-content）
   * @default 'between'
   */
  content?: FlexAlign

  /**
   * 是否允许换行
   * @default false
   */
  wrap?: boolean

  /**
   * 交叉轴对齐方式（align-items）
   * @default 'center'
   */
  align?: FlexAlign

  /**
   * 主轴对齐方式（justify-content）
   * @default 'between'
   */
  justify?: FlexAlign

  /**
   * 自定义 CSS 类名
   */
  class?: string
}
