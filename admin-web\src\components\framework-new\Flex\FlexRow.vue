<script setup lang="ts">
import type { FlexAlign, FrameworkFlexRowProps } from './type'
import { computed, defineProps } from 'vue'

const props = withDefaults(defineProps<FrameworkFlexRowProps>(), {
  gap: 'gap-12px',
  content: 'between',
  wrap: false,
  align: 'center',
  justify: 'between',
})

const classes = computed(() => {
  const baseClasses = ['flex', 'flex-row']

  // 处理 justify-content
  const justifyMap: Record<FlexAlign, string> = {
    center: 'justify-center',
    start: 'justify-start',
    end: 'justify-end',
    between: 'justify-between',
    around: 'justify-around',
    evenly: 'justify-evenly',
  }

  // 处理 align-items
  const alignMap: Record<FlexAlign, string> = {
    center: 'items-center',
    start: 'items-start',
    end: 'items-end',
    between: 'items-between',
    around: 'items-around',
    evenly: 'items-evenly',
  }

  // 添加 justify 类
  if (props.justify) {
    baseClasses.push(justifyMap[props.justify])
  }

  // 添加 align 类
  if (props.align) {
    baseClasses.push(alignMap[props.align])
  }

  // 处理换行
  if (props.wrap) {
    baseClasses.push('flex-wrap')
  }
  else {
    baseClasses.push('flex-nowrap')
  }

  // 处理间距
  if (props.gap) {
    baseClasses.push(props.gap)
  }
  //   if (props.gap && props.gap !== '0px') {
  //     // 将 gap 值转换为 unocss 类
  //     if (props.gap.includes('px')) {
  //       const gapValue = props.gap.replace('px', '')
  //       baseClasses.push(`gap-${gapValue}px`)
  //     }
  //     else {
  //       baseClasses.push(`gap-${props.gap}`)
  //     }
  //   }

  // 添加自定义类
  if (props.class) {
    baseClasses.push(props.class)
  }

  return baseClasses
})
</script>

<template>
  <div :class="classes">
    <slot />
  </div>
</template>
