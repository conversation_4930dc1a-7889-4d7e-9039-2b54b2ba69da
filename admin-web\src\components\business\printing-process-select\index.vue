/**
 * 印刷工艺选择组件
 */
<script setup lang="ts">
import type { PrintingProcessQueryParam } from '@/api/business/printing-process/model/printing-process-form-model'
import type { PrintingProcessModel } from '@/api/business/printing-process/model/printing-process-model'
import type { SelectProps } from 'ant-design-vue'
import { printingProcessApi } from '@/api/business/printing-process/printing-process-api'
import { message } from 'ant-design-vue'
import { ref, watch } from 'vue'

// 组件属性定义
interface PrintingProcessSelectProps extends Partial<PrintingProcessQueryParam> {
  disabled?: boolean
  placeholder?: string
  allowClear?: boolean
  value?: number | undefined
  modelValue?: number | undefined
}

// 组件属性默认值
const props = withDefaults(defineProps<PrintingProcessSelectProps>(), {
  status: true, // 默认只查询启用状态的工艺
  disabled: false,
  placeholder: '请选择印刷工艺',
  allowClear: true,
  value: undefined,
  modelValue: undefined,
  code: '',
  name: '',
  price: 0,
  description: '',
})

// 组件事件
const emit = defineEmits<{
  // 工艺选择事件，提供完整的工艺对象
  (e: 'select', printingProcess: PrintingProcessModel): void
  // 工艺清除事件
  (e: 'clear'): void
  // v-model 支持
  (e: 'update:modelValue', value: number | undefined): void
}>()

// 组件状态
const loading = ref(false)
const printingProcessList = ref<PrintingProcessModel[]>([])

// 监听查询条件变化，自动加载数据
watch(
  [() => props.code, () => props.name, () => props.status],
  async () => {
    await loadPrintingProcessList()
  },
  { immediate: true },
)

/**
 * 加载印刷工艺列表
 */
async function loadPrintingProcessList(): Promise<void> {
  try {
    loading.value = true

    const queryParams: PrintingProcessQueryParam = {
      code: props.code,
      name: props.name,
      status: props.status,
      price: props.price,
      description: props.description,
    }

    // 处理 status 参数，确保类型正确
    // 在 API 调用中，我们不修改 queryParams.status 的值
    // 因为 API 应该已经处理了布尔值的情况

    const response = await printingProcessApi.printingProcessList(queryParams)

    if (response.success && response.data) {
      printingProcessList.value = response.data
    }
  }
  catch (error) {
    message.error('获取印刷工艺失败')
    console.error('获取印刷工艺失败', error)
  }
  finally {
    loading.value = false
  }
}

/**
 * 格式化印刷工艺选项文本
 */
function formatPrintingProcessOption(process: PrintingProcessModel): string {
  return `${process.name || ''}  ${process.price ? `¥${process.price}/m²` : ''} `
}

/**
 * 处理印刷工艺选择变更
 */
function handleSelectionChange(value: SelectProps['value']): void {
  // 将值转换为数字或undefined
  const processId = value === null || value === undefined || value === ''
    ? undefined
    : Number(value)

  // 更新v-model值
  emit('update:modelValue', processId)

  // 如果选择了印刷工艺，触发select事件
  if (processId !== undefined) {
    const selectedProcess = printingProcessList.value.find(item => item.id === processId)
    if (selectedProcess) {
      emit('select', selectedProcess)
    }
  }
  // 如果清除了选择，触发clear事件
  else {
    emit('clear')
  }
}

// 公开组件方法和属性
defineExpose({
  printingProcessList,
  loadPrintingProcessList,
})
</script>

<template>
  <a-select
    :loading="loading"
    :disabled="disabled"
    :placeholder="placeholder"
    :allow-clear="allowClear"
    style="width: 100%"
    :value="value !== undefined ? value : modelValue"
    :options="printingProcessList.map(process => ({
      value: process.id,
      label: formatPrintingProcessOption(process),
      process,
    }))"
    @change="handleSelectionChange"
  >
    <template #notFoundContent>
      <span v-if="loading">加载中...</span>
      <span v-else>未找到相关印刷工艺</span>
    </template>
  </a-select>
</template>
