<script lang="ts" setup>
import type { AxTableService } from './type'
import { AxPagination as Pagination } from '@/components/framework-new/AxPagination'
import { CRUD_KEY } from '@/service/BaseCrudService'
import { inject } from 'vue'

const service = inject<AxTableService>(CRUD_KEY)!
if (!service) {
  throw new Error('AxTableService is not injected')
}
</script>

<template>
  <a-table
    size="small"
    :data-source="service.getRawTableData()"
    :columns="service.columns"
    :row-key="service.rowKey"
    bordered
    :pagination="false"
    :loading="service.tableLoading"
    :scroll="service.scroll"
    :row-selection="{
      selectedRowKeys: service.selectedRowKeyList,
      onChange: service.onSelectChange,
    }"
    @change="service.onChange"
  >
    <template v-for="name in Object.keys($slots)" #[name]="slotProps">
      <slot :name="name" v-bind="slotProps" />
    </template>
  </a-table>
  <Pagination />
</template>
