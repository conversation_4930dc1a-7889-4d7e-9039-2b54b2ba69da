<script setup lang="ts">
import type { IconAnchorProps } from './type'
import CustomIcon from './CustomIcon.vue'

const {
  label = 'undefined',
  iconType = 'UserOutlined',
  spin = false,
  rotate = 0,
  color = 'inherit',
} = defineProps<IconAnchorProps>()

const style = computed(() => {
  return {
    color,
  }
})
</script>

<template>
  <a v-bind="$attrs" :style="style">
    <CustomIcon :icon-type="iconType" :spin="spin" :rotate="rotate" />
    {{ label }}
  </a>
</template>
