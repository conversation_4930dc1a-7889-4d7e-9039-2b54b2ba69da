<script lang="ts" setup>
import type { FormInstance } from 'ant-design-vue/es/form'
import type { OrderService } from '../service/OrderService'
import { inject, ref } from 'vue'
import { Form, FormColumn, FormItem, FormRow } from '@/components/framework-new/Form'
import { InputText } from '@/components/framework-new/Input'
// 框架组件
import { Modal } from '@/components/framework-new/Modal'
import { Spin } from '@/components/framework-new/Spin'
import DictSelect from '@/components/support/dict-select/index.vue'
import { CRUD_KEY } from '@/service/BaseCrudService'
// 表单验证规则
import { rules } from '../config/rule'

// 获取服务实例
const orderService = inject<OrderService>(CRUD_KEY)!
// 表单引用
const formRef = ref<FormInstance>()

// 表单提交
async function handleSubmit() {
  if (formRef.value)
    await formRef.value.validate()
  // 验证通过后提交（使用带自动刷新的提交方法）
  await orderService.submitFormAndRefresh()
}
</script>

<template>
  <Modal
    :open="orderService.formOpen"
    :title="orderService.formTitle"
    :confirm-loading="orderService.submitLoading"
    :mask-closable="false"
    :destroy-on-close="true"
    width="1000px"
    @ok="handleSubmit"
    @cancel="orderService.closeForm()"
  >
    <Spin :spinning="orderService.formLoading">
      <Form ref="formRef" :model="orderService.formData" :rules="rules" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
        <!-- 基本信息 -->
        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="订单编号" name="orderNo">
              <InputText v-model="orderService.formData.orderNo" placeholder="请输入订单编号" :disabled="orderService.formType === 'edit'" allow-clear />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="开单方式" name="orderType">
              <DictSelect
                v-model:value="orderService.formData.orderType" key-code="order_type" placeholder="请选择开单方式"
                width="100%"
              />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="客户名称" name="customerName">
              <InputText v-model="orderService.formData.customerName" placeholder="请输入客户名称" allow-clear />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="客户编码" name="customerCode">
              <InputText v-model="orderService.formData.customerCode" placeholder="请输入客户编码" allow-clear />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="订单日期" name="orderDate">
              <a-date-picker v-model:value="orderService.formData.orderDate" placeholder="请选择订单日期" style="width: 100%" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="交货日期" name="deliveryDate">
              <a-date-picker v-model:value="orderService.formData.deliveryDate" placeholder="请选择交货日期" style="width: 100%" />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="订单状态" name="orderStatus">
              <DictSelect
                v-model:value="orderService.formData.orderStatus" key-code="order_status" placeholder="请选择订单状态"
                width="100%"
              />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="付款方式" name="paymentMethod">
              <DictSelect
                v-model:value="orderService.formData.paymentMethod" key-code="payment_method" placeholder="请选择付款方式"
                width="100%"
              />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="付款条件" name="paymentTerms">
              <DictSelect
                v-model:value="orderService.formData.paymentTerms" key-code="payment_terms" placeholder="请选择付款条件"
                width="100%"
              />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="币种" name="currency">
              <InputText v-model="orderService.formData.currency" placeholder="请输入币种" allow-clear />
            </FormItem>
          </FormColumn>
        </FormRow>

        <!-- 金额信息 -->
        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="商品小计" name="subtotalAmount">
              <a-input-number v-model:value="orderService.formData.subtotalAmount" placeholder="请输入商品小计" :precision="2" style="width: 100%" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="折扣金额" name="discountAmount">
              <a-input-number v-model:value="orderService.formData.discountAmount" placeholder="请输入折扣金额" :precision="2" style="width: 100%" />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="税额" name="taxAmount">
              <a-input-number v-model:value="orderService.formData.taxAmount" placeholder="请输入税额" :precision="2" style="width: 100%" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="运费" name="shippingFee">
              <a-input-number v-model:value="orderService.formData.shippingFee" placeholder="请输入运费" :precision="2" style="width: 100%" />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="最终金额" name="finalAmount">
              <a-input-number v-model:value="orderService.formData.finalAmount" placeholder="请输入最终金额" :precision="2" style="width: 100%" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="已付金额" name="paidAmount">
              <a-input-number v-model:value="orderService.formData.paidAmount" placeholder="请输入已付金额" :precision="2" style="width: 100%" />
            </FormItem>
          </FormColumn>
        </FormRow>

        <!-- 联系信息 -->
        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="联系人" name="contactName">
              <InputText v-model="orderService.formData.contactName" placeholder="请输入联系人" allow-clear />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="联系电话" name="contactPhone">
              <InputText v-model="orderService.formData.contactPhone" placeholder="请输入联系电话" allow-clear />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="联系手机" name="contactMobile">
              <InputText v-model="orderService.formData.contactMobile" placeholder="请输入联系手机" allow-clear />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="销售员" name="salesPersonName">
              <InputText v-model="orderService.formData.salesPersonName" placeholder="请输入销售员姓名" allow-clear />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="订单来源" name="orderSource">
              <DictSelect
                v-model:value="orderService.formData.orderSource" key-code="order_source" placeholder="请选择订单来源"
                width="100%"
              />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="优先级" name="priorityLevel">
              <DictSelect
                v-model:value="orderService.formData.priorityLevel" key-code="priority_level" placeholder="请选择优先级"
                width="100%"
              />
            </FormItem>
          </FormColumn>
        </FormRow>

        <!-- 地址信息 -->
        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="收货地址" name="shippingAddress">
              <a-textarea v-model:value="orderService.formData.shippingAddress" placeholder="请输入收货地址" :rows="2" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="发票地址" name="billingAddress">
              <a-textarea v-model:value="orderService.formData.billingAddress" placeholder="请输入发票地址" :rows="2" />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="24">
            <FormItem label="备注" name="remark" :label-col="{ span: 3 }" :wrapper-col="{ span: 20 }">
              <a-textarea v-model:value="orderService.formData.remark" placeholder="请输入备注" :rows="3" />
            </FormItem>
          </FormColumn>
        </FormRow>
      </Form>
    </Spin>
  </Modal>
</template>

<style scoped>
.radio-style {
  margin-right: 16px;
}
</style>
