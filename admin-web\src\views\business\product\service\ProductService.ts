import type { ProductFormParam, ProductPageParam, ProductResult } from '@/api/business/product/model/product-form-model'
import type { SkuFormParam, SkuResult } from '@/api/business/sku/model/sku-form-model'
import type { SkuModel } from '@/api/business/sku/model/sku-model'
import { message } from 'ant-design-vue'
import { computed, reactive, ref } from 'vue'
import { productApi } from '@/api/business/product/product-api'
import { skuApi } from '@/api/business/sku/sku-api'
import { BaseCrudService } from '@/service/BaseCrudService'
import { productColumns } from '@/views/business/product/config/productColumns'

/**
 * 商品服务
 * 提供商品相关的业务逻辑和数据管理
 */
export class ProductService extends BaseCrudService<ProductResult, ProductFormParam, ProductPageParam> {
  // 分类选项
  private _categoryOptions = ref<Array<{ id: number, name: string }>>([])
  // 品牌选项
  private _brandOptions = ref<Array<{ id: number, name: string }>>([])
  // SKU数据映射，key为商品ID，value为SKU列表
  private _skuDataMap = reactive<Record<number, SkuResult[]>>({})
  // 商品展开状态，key为商品ID，value为是否展开
  private _expandedRowKeys = ref<number[]>([])
  // SKU加载状态映射
  private _skuLoadingMap = reactive<Record<number, boolean>>({})
  // 当前正在编辑的SKU数据
  private _currentEditingSku = ref<SkuResult | null>(null)
  // SKU表单显示控制
  private _skuFormVisible = ref<boolean>(false)
  // SKU表单类型：'add'或'edit'
  private _skuFormType = ref<'add' | 'edit'>('add')
  // SKU表单标题
  private _skuFormTitle = computed(() => this._skuFormType.value === 'add' ? '新增SKU' : '编辑SKU')
  // 是否已经加载过SKU数据的商品ID集合，用于避免重复加载
  private _loadedSkuProductIds = new Set<number>()

  // Getters
  get categoryOptions() { return this._categoryOptions.value }
  get brandOptions() { return this._brandOptions.value }
  get skuDataMap() { return this._skuDataMap }
  get expandedRowKeys() { return this._expandedRowKeys.value }
  get skuLoadingMap() { return this._skuLoadingMap }
  get currentEditingSku() { return this._currentEditingSku.value }
  get skuFormVisible() { return this._skuFormVisible.value }
  get skuFormType() { return this._skuFormType.value }
  get skuFormTitle() { return this._skuFormTitle.value }

  constructor() {
    // 初始化服务
    super(
      '商品', // 业务名称
      productColumns,
      {
        // 使用已有的API
        queryPage: productApi.productPage,
        getDetail: (id: number | string) => productApi.productDetail(Number(id)),
        add: productApi.addProduct,
        update: productApi.updateProduct,
        delete: (id: number | string) => productApi.deleteProduct(Number(id)),
        batchDelete: ids => productApi.batchDeleteProduct(ids.map(id => Number(id))),
        import: productApi.importProduct,
        export: productApi.exportProduct,
      },
    )

    // 初始化分类和品牌选项数据
    this.loadCategoryOptions()
    this.loadBrandOptions()
  }

  /**
   * 加载分类选项
   */
  async loadCategoryOptions() {
    // 模拟数据，实际开发中需要从API获取
    this._categoryOptions.value = [
      { id: 1, name: '食品' },
      { id: 2, name: '饮料' },
      { id: 3, name: '电子产品' },
      { id: 4, name: '家居用品' },
      { id: 5, name: '服装' },
    ]
  }

  /**
   * 加载品牌选项
   */
  async loadBrandOptions() {
    // 模拟数据，实际开发中需要从API获取
    this._brandOptions.value = [
      { id: 1, name: '品牌A' },
      { id: 2, name: '品牌B' },
      { id: 3, name: '品牌C' },
      { id: 4, name: '品牌D' },
      { id: 5, name: '品牌E' },
    ]
  }

  /**
   * 处理分类选择变化
   * @param categoryId 选择的分类ID
   */
  handleCategoryChange(categoryId: number) {
    const selectedCategory = this._categoryOptions.value.find(item => item.id === categoryId)
    if (selectedCategory && this.formData) {
      this.formData.categoryName = selectedCategory.name
    }
  }

  /**
   * 分类变化处理（用于查询条件）
   * @param categoryId 选择的分类ID
   */
  onCategoryChange = (categoryId: number) => {
    this.queryParam.categoryId = categoryId
    // 可以在这里添加其他逻辑，比如清空其他相关字段
    console.log('选中的分类ID:', categoryId)
  }

  /**
   * 处理品牌选择变化
   * @param brandId 选择的品牌ID
   */
  handleBrandChange(brandId: number) {
    const selectedBrand = this._brandOptions.value.find(item => item.id === brandId)
    if (selectedBrand && this.formData) {
      this.formData.brandName = selectedBrand.name
    }
  }

  /**
   * 获取状态对应的颜色
   * @param status 状态
   */
  getStatusColor(status?: boolean): string {
    return status ? 'success' : 'error'
  }

  /**
   * 格式化状态
   * @param status 状态值
   */
  formatStatus(status?: boolean): string {
    return status ? '上架' : '下架'
  }

  /**
   * 获取SKU状态颜色
   * @param status SKU状态
   */
  getSkuStatusColor(status?: number): string {
    switch (status) {
      case 0: return 'error' // 下架
      case 1: return 'success' // 上架
      case 2: return 'warning' // 缺货
      default: return 'default'
    }
  }

  /**
   * 格式化SKU状态
   * @param status SKU状态值
   */
  formatSkuStatus(status?: number): string {
    switch (status) {
      case 0: return '下架'
      case 1: return '上架'
      case 2: return '缺货'
      default: return '未知'
    }
  }

  /**
   * 处理表格行展开/收起
   * @param expanded 是否展开
   * @param record 表格行数据
   */
  handleExpandedRow = async (expanded: boolean, record: ProductResult) => {
    if (!record || !record.id)
      return

    const productId = record.id

    if (expanded) {
      // 添加到展开行keys
      if (!this._expandedRowKeys.value.includes(productId)) {
        this._expandedRowKeys.value = [...this._expandedRowKeys.value, productId]
      }
      // 加载SKU数据
      await this.loadSkuData(productId)
    }
    else {
      // 从展开行keys中移除 - 立即收起
      this._expandedRowKeys.value = this._expandedRowKeys.value.filter(key => key !== productId)
    }
  }

  /**
   * 加载商品对应的SKU数据
   * @param productId 商品ID
   */
  async loadSkuData(productId: number) {
    if (!productId)
      return

    // 如果已经加载过该商品的SKU数据，则不重复加载
    if (this._loadedSkuProductIds.has(productId) && this._skuDataMap[productId]) {
      return
    }

    // 设置加载状态
    this._skuLoadingMap[productId] = true

    try {
      // 从分页查询结果中获取SKU数据
      const tableData = this.getRawTableData() as ProductResult[]
      const productData = tableData.find(item => item.id === productId)

      if (productData && productData.skuList && productData.skuList.length > 0) {
        // 从产品数据的skuList转换为SkuResult类型
        const skuResults: SkuResult[] = productData.skuList.map((sku: SkuModel) => {
          // 创建一个新对象并转换status字段为字符串类型
          const result = {
            ...sku,
            // 确保status是字符串类型: 1上架，0下架
            status: typeof sku.status === 'string'
              ? sku.status
              : (sku.status === true ? '1' : '0'),
          }
          return result as unknown as SkuResult
        })

        // 使用分页查询中已包含的SKU数据
        this._skuDataMap[productId] = skuResults
        this._loadedSkuProductIds.add(productId)
      }
      else {
        // 没有找到SKU数据
        this._skuDataMap[productId] = []
        this._loadedSkuProductIds.add(productId)
      }
    }
    catch (error) {
      console.error('Failed to load SKU data:', error)
      message.error('加载SKU数据失败')
    }
    finally {
      // 清除加载状态
      this._skuLoadingMap[productId] = false
    }
  }

  /**
   * 获取指定商品的SKU数量
   * @param productId 商品ID
   */
  getSkuCount(productId?: number): number {
    if (!productId || !this._skuDataMap[productId])
      return 0
    return this._skuDataMap[productId].length
  }

  /**
   * 获取指定商品的SKU数据
   * @param productId 商品ID
   */
  getSkuData(productId?: number): SkuResult[] {
    if (!productId || !this._skuDataMap[productId])
      return []
    return this._skuDataMap[productId]
  }

  /**
   * 判断指定商品的SKU数据是否正在加载
   * @param productId 商品ID
   */
  isSkuLoading(productId?: number): boolean {
    if (!productId)
      return false
    return !!this._skuLoadingMap[productId]
  }

  /**
   * 全部展开或收起
   * @param expand 是否展开
   */
  expandAllRows(expand: boolean) {
    if (expand) {
      // 展开所有行 - 逐个展开，避免一次性操作造成卡顿
      const tableData = this.getRawTableData()

      if (!tableData || !tableData.length)
        return

      // 限制最大展开数量，避免卡顿
      const maxItems = Math.min(tableData.length, 10)

      // 找出有效的商品ID
      const validIds = tableData
        .slice(0, maxItems)
        .filter(item => item && item.id !== undefined)
        .map(item => item.id as number)

      if (validIds.length) {
        // 设置展开状态
        this.setExpandedRowKeys(validIds)

        // 分批加载SKU数据，避免同时发起过多请求
        validIds.forEach((id, index) => {
          if (id) {
            // 延迟加载，错开请求时间
            setTimeout(() => {
              this.loadSkuData(id)
            }, index * 100) // 每个请求间隔100ms
          }
        })
      }
    }
    else {
      // 收起所有行
      this.setExpandedRowKeys([])
    }
  }

  /**
   * 打开新增SKU表单
   * @param productId 商品ID
   */
  openAddSkuForm(productId: number) {
    this._skuFormType.value = 'add'
    this._currentEditingSku.value = {
      productId,
      // 使用字符串类型的状态: '1'表示上架
      status: '1',
      stock: 0, // 默认库存为0
    }
    this._skuFormVisible.value = true
  }

  /**
   * 打开SKU表单进行编辑
   * @param sku SKU数据
   */
  openEditSkuForm(sku: SkuResult) {
    this._skuFormType.value = 'edit'
    this._currentEditingSku.value = { ...sku }
    this._skuFormVisible.value = true
  }

  /**
   * 关闭SKU表单
   */
  closeSkuForm() {
    this._skuFormVisible.value = false
    this._currentEditingSku.value = null
  }

  /**
   * 保存SKU数据
   */
  async saveSkuData() {
    if (!this._currentEditingSku.value)
      return

    try {
      const skuData = this._currentEditingSku.value as SkuFormParam
      const productId = skuData.productId!

      if (this._skuFormType.value === 'add') {
        await skuApi.addSku(skuData)
        message.success('SKU添加成功')
      }
      else {
        await skuApi.updateSku(skuData)
        message.success('SKU更新成功')
      }

      // 从已加载集合中移除，强制下次展开时重新加载
      this._loadedSkuProductIds.delete(productId)

      // 重新加载SKU数据
      await this.loadSkuData(productId)
      this.closeSkuForm()
    }
    catch (error) {
      console.error('Failed to save SKU data:', error)
      message.error('保存SKU数据失败')
    }
  }

  /**
   * 删除SKU
   * @param sku SKU数据
   */
  async deleteSku(sku: SkuResult) {
    if (!sku.id || !sku.productId)
      return

    try {
      await skuApi.deleteSku(sku.id)
      message.success('SKU删除成功')

      // 从已加载集合中移除，强制下次展开时重新加载
      this._loadedSkuProductIds.delete(sku.productId)

      // 重新加载SKU数据
      await this.loadSkuData(sku.productId)
    }
    catch (error) {
      console.error('Failed to delete SKU:', error)
      message.error('删除SKU失败')
    }
  }

  /**
   * 更新SKU状态
   * @param sku SKU数据
   * @param newStatus 新状态（0:下架, 1:上架, 2:缺货）
   */
  async updateSkuStatus(sku: SkuResult, newStatus: number) {
    if (!sku.id || !sku.productId)
      return

    try {
      const updatedSku: SkuFormParam = {
        ...sku,
        // 转换为字符串类型: '1'表示上架，其他为下架或缺货
        status: newStatus === 1 ? '1' : '0',
      }
      await skuApi.updateSku(updatedSku)
      message.success('状态更新成功')

      // 从已加载集合中移除，强制下次展开时重新加载
      this._loadedSkuProductIds.delete(sku.productId)

      // 重新加载SKU数据
      await this.loadSkuData(sku.productId)
    }
    catch (error) {
      console.error('Failed to update SKU status:', error)
      message.error('更新状态失败')
    }
  }

  /**
   * 更新SKU库存
   * @param sku SKU数据
   * @param newStock 新库存
   */
  async updateSkuStock(sku: SkuResult, newStock: number) {
    if (!sku.id || !sku.productId)
      return

    try {
      const updatedSku: SkuFormParam = {
        ...sku,
        stock: newStock,
      }
      await skuApi.updateSku(updatedSku)
      message.success('库存更新成功')

      // 直接更新本地数据，无需重新加载
      if (this._skuDataMap[sku.productId]) {
        const skuIndex = this._skuDataMap[sku.productId].findIndex(item => item.id === sku.id)
        if (skuIndex !== -1) {
          this._skuDataMap[sku.productId][skuIndex].stock = newStock
        }
      }
    }
    catch (error) {
      console.error('Failed to update SKU stock:', error)
      message.error('更新库存失败')
    }
  }

  /**
   * 清空缓存和状态数据
   * 在组件销毁前调用
   */
  clearState() {
    this._skuDataMap = {}
    this._loadedSkuProductIds.clear()
    this._expandedRowKeys.value = []
    this._skuLoadingMap = {}
  }

  /**
   * 设置展开行的键
   * @param keys 展开行的ID数组
   */
  setExpandedRowKeys(keys: number[]) {
    this._expandedRowKeys.value = keys
  }
}

export const productService = new ProductService()
