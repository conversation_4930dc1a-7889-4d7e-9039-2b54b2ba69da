export type DictColor = 'blue' | 'green' | 'red' | 'yellow' | 'purple' | 'orange' | 'gray' | 'default'

export interface DictSelectProps {
  keyCode?: string
  value?: string | number | boolean
  mode?: 'multiple' | 'tags' | 'default'
  placeholder?: string
  size?: 'small' | 'middle' | 'large'
  disabled?: boolean
}

export interface DictTagProps {
  keyCode?: string
  valueCode?: string
  color?: DictColor
}

export interface DictValueProps {
  keyCode: string
  valueCode: string | null | undefined
}