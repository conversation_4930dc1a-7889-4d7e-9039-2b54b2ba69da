import type { BoxFormParam, BoxResult } from '@/api/business/box/model/box-form-model'
import type { InjectionKey } from 'vue'
import { boxApi } from '@/api/business/box/box-api'
import { FormProcessor } from '@/service/internal/FormProcessor'
import { FormulaTranslator } from '@/views/business/erp/box-quotation-calculator/utils/formula/formula_translator'
import { message } from 'ant-design-vue'
import { inject, provide } from 'vue'

// 注入键
export const BOX_FORM_SERVICE_KEY: InjectionKey<BoxFormService> = Symbol('box-form-service')

/**
 * 纸箱规格表单服务
 * 继承自 FormProcessor，处理纸箱规格的增删改查表单逻辑
 */
export class BoxFormService extends FormProcessor<BoxResult, BoxFormParam> {
  constructor() {
    super(
      '纸箱规格', // 业务名称
      {
        // API 配置
        getDetail: boxApi.boxDetail, // 传入获取详情的 API 函数引用
        add: boxApi.addBox, // 传入新增的 API 函数引用
        update: boxApi.updateBox, // 传入更新的 API 函数引用
      },
    )

    // 设置自定义的默认表单数据
    this.setCustomDefaultFormData(() => ({
      id: undefined,
      boxTypeCode: '',
      boxTypeName: '',
      boxTypeDesc: '',
      imageUrl: '',
      isActive: true,
      paramsJson: '',
      sort: 0,
      boxConfig: {
        needDieCutting: false,
        needPrintingPlate: false,
        doorWidthFormula: '(长+宽)*2+舌头长度',
        totalLengthFormula: '(长+宽)*2+舌头长度',
        doorWidthFormulaDesc: '',
        totalLengthFormulaDesc: '',
      },
    }))
  }

  /**
   * 静态工厂方法：创建实例并提供给后代组件
   * @param key 注入键，默认使用BOX_FORM_SERVICE_KEY
   * @returns 创建的服务实例
   */
  static createAndProvide(key: InjectionKey<BoxFormService> = BOX_FORM_SERVICE_KEY): BoxFormService {
    const instance = new BoxFormService()
    provide(key, instance)
    return instance
  }

  /**
   * 覆盖 beforeSubmit 方法，加入公式验证和 paramsJson 序列化
   */
  protected override beforeSubmit(formData: BoxFormParam): BoxFormParam {
    // 1. 验证公式
    const { valid, message: errorMsg } = this.validateFormulas(formData)
    if (!valid) {
      message.error(errorMsg)
      // 抛出错误会阻止 FormProcessor 继续执行提交
      throw new Error(errorMsg)
    }

    // 2. 序列化 boxConfig 到 paramsJson
    formData.paramsJson = JSON.stringify(formData.boxConfig)

    // 3. 调用父类的 beforeSubmit (确保调用)
    return super.beforeSubmit(formData)
  }

  /**
   * 验证公式是否合法 (从 modal 中提取)
   * @param formData 表单数据
   */
  private validateFormulas(formData: BoxFormParam): { valid: boolean, message: string } {
    // 获取公式
    const { doorWidthFormula, totalLengthFormula } = formData.boxConfig

    // 验证门宽公式
    if (doorWidthFormula && !FormulaTranslator.validateChineseFormula(doorWidthFormula)) {
      const unknownTerms = FormulaTranslator.detectUnknownTerms(doorWidthFormula)
      let errorMsg = '门宽公式不合法'
      if (unknownTerms.length > 0) {
        errorMsg += `，包含未知字段: ${unknownTerms.join(', ')}`
      }
      return { valid: false, message: errorMsg }
    }

    // 验证总长公式
    if (totalLengthFormula && !FormulaTranslator.validateChineseFormula(totalLengthFormula)) {
      const unknownTerms = FormulaTranslator.detectUnknownTerms(totalLengthFormula)
      let errorMsg = '总长公式不合法'
      if (unknownTerms.length > 0) {
        errorMsg += `，包含未知字段: ${unknownTerms.join(', ')}`
      }
      return { valid: false, message: errorMsg }
    }

    return { valid: true, message: '' }
  }
}

/**
 * 注入纸箱规格表单服务
 * @param key 注入键，默认使用BOX_FORM_SERVICE_KEY
 * @returns 注入的服务实例
 */
export function injectBoxFormService(key: InjectionKey<BoxFormService> = BOX_FORM_SERVICE_KEY): BoxFormService {
  const service = inject(key)
  if (!service) {
    // 确认祖先组件已提供服务
    throw new Error('BoxFormService 未提供。请确保在祖先组件中调用了 service.provide()。')
  }
  return service
}
