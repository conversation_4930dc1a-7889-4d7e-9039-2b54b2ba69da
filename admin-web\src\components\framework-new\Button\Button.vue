<script setup lang="ts">
import type { ButtonProps } from './type'

const {
  type = 'default',
  size = 'middle',
  disabled = false,
  loading = false,
} = defineProps<ButtonProps>()
</script>

<template>
  <a-button
    :type="type"
    :size="size"
    :disabled="disabled"
    :loading="loading"
  >
    <template #icon>
      <slot name="icon" />
    </template>
    <slot />
  </a-button>
</template>
