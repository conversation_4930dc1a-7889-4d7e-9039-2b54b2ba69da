<script lang="ts" setup>
import type { TableColumnsType } from 'ant-design-vue'
import {
  DeleteOutlined,
  EditOutlined,
  EyeOutlined,
  PlusOutlined,
  ReloadOutlined,
  SearchOutlined,
} from '@ant-design/icons-vue'
import { onBeforeUnmount, ref } from 'vue'
import CategoryTreeSelect from '@/components/business/category-tree-select/index.vue'
import ImportExcelModal from '@/components/framework/ax-import-excel-modal/index.vue'
import Pagination from '@/components/framework/ax-pagination/index.vue'
import TableOperateButtons from '@/components/framework/ax-table-operate-buttons/index.vue'
import BooleanRadio from '@/components/framework/boolean-radio/index.vue'
import DictSelect from '@/components/support/dict-select/index.vue'
import DictValue from '@/components/support/dict-value/index.vue'
import TableOperator from '@/components/support/table-operator/index.vue'
import { TABLE_ID_CONST } from '@/constants/support/table-id-const'
import ProductDetail from './components/ProductDetail.vue'
import ProductForm from './components/ProductForm.vue'
import SkuForm from './components/SkuForm.vue'
import { skuColumns } from './config/productColumns'
import { productService } from './service/ProductService'

productService.provide()

const columns = ref<TableColumnsType>(productService.columns) // 传入 productService 实例

// 组件销毁前清空SKU缓存和状态
onBeforeUnmount(() => {
  productService.clearState()
})
</script>

<template>
  <!---------- 查询参数 begin ----------->
  <a-form v-privilege="'product:query'" class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="商品编码" class="smart-query-form-item">
        <a-input
          v-model:value="productService.queryParam.productCode" style="width: 150px"
          placeholder="请输入商品编码"
        />
      </a-form-item>

      <a-form-item label="商品名称" class="smart-query-form-item">
        <a-input v-model:value="productService.queryParam.name" style="width: 150px" placeholder="请输入商品名称" />
      </a-form-item>

      <a-form-item label="主分类" class="smart-query-form-item">
        <CategoryTreeSelect
          v-model:value="productService.queryParam.categoryId"
          category-type="product"
          placeholder="请选择主分类"
          width="150px"
          @change="productService.onCategoryChange"
        />
      </a-form-item>

      <a-form-item label="品牌" class="smart-query-form-item">
        <a-select
          v-model:value="productService.queryParam.brandId"
          style="width: 150px"
          placeholder="请选择品牌"
          allow-clear
        >
          <a-select-option
            v-for="brand in productService.brandOptions"
            :key="brand.id"
            :value="brand.id"
          >
            {{ brand.name }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="商品来源" class="smart-query-form-item">
        <DictSelect
          v-model:value="productService.queryParam.sourceType"
          key-code="product_source_type"
          placeholder="请选择商品来源"
          width="150px"
        />
      </a-form-item>

      <a-form-item label="是否原材料" class="smart-query-form-item">
        <BooleanRadio v-model:model-value="productService.queryParam.isRawMaterial" />
      </a-form-item>

      <a-form-item label="状态" class="smart-query-form-item">
        <DictSelect
          v-model:value="productService.queryParam.status"
          key-code="product_status"
          placeholder="请选择状态"
          width="120px"
        />
      </a-form-item>

      <a-form-item class="smart-query-form-item">
        <a-button-group>
          <a-button type="primary" @click="productService.onSearch">
            <template #icon>
              <SearchOutlined />
            </template>
            查询
          </a-button>
          <a-button @click="productService.resetQuery">
            <template #icon>
              <ReloadOutlined />
            </template>
            重置
          </a-button>
        </a-button-group>
      </a-form-item>
    </a-row>
  </a-form>
  <!---------- 查询参数 end ----------->

  <a-card size="small" :bordered="false" :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <a-row class="smart-table-btn-block">
      <div class="smart-table-operate-block">
        <TableOperateButtons
          :operate-param="{
            add: {
              privilege: 'product:add',
            },
            batchDelete: {
              privilege: 'product:delete',
            },
            import: {
              privilege: 'product:import',
            },
            export: {
              privilege: 'product:export',
            },
          }"
        />
        <a-button v-privilege="'product:query'" class="ml-2" @click="productService.expandAllRows(true)">
          <template #icon>
            <PlusOutlined />
          </template>
          部分展开
        </a-button>
        <a-button v-privilege="'product:query'" class="ml-2" @click="productService.expandAllRows(false)">
          <template #icon>
            <PlusOutlined :rotate="90" />
          </template>
          全部收起
        </a-button>
      </div>
      <div class="smart-table-setting-block">
        <TableOperator
          v-model="columns"
          :table-id="TABLE_ID_CONST.BUSINESS.ERP.GOODS"
          :refresh="productService.queryPage"
        />
      </div>
    </a-row>
    <!---------- 表格操作行 end ----------->

    <ax-table-expand>
      <template #expandedRowRender="{ record }">
        <a-spin :spinning="productService.isSkuLoading(record.id)">
          <div class="sku-expanded-content">
            <a-table
              size="small"
              :data-source="productService.getSkuData(record.id)"
              :columns="skuColumns"
              :pagination="false"
              row-key="id"
              bordered
            >
              <template #bodyCell="{ column, record: skuRecord }">
                <!-- 状态列 -->
                <template v-if="column.dataIndex === 'status'">
                  <a-tag :color="skuRecord.status ? 'success' : 'error'">
                    {{ skuRecord.status ? '上架' : '下架' }}
                  </a-tag>
                </template>

                <!-- 库存列 -->
                <template v-if="column.dataIndex === 'stock'">
                  <a-input-number
                    v-model:value="skuRecord.stock"
                    :min="0"
                    style="width: 80px"
                    size="small"
                    @blur="productService.updateSkuStock(skuRecord, skuRecord.stock || 0)"
                  />
                </template>
              </template>
            </a-table>
          </div>
        </a-spin>
      </template>

      <template #bodyCell="{ column, record }">
        <!-- 是否原材料列 -->
        <template v-if="column.dataIndex === 'isRawMaterial'">
          <a-tag :color="record.isRawMaterial ? 'blue' : 'default'">
            {{ record.isRawMaterial ? '是' : '否' }}
          </a-tag>
        </template>

        <!-- 来源列 -->
        <template v-if="column.dataIndex === 'sourceType'">
          <DictValue key-code="product_source_type" :value-code="record.sourceType" />
        </template>

        <!-- 状态列 -->
        <template v-if="column.dataIndex === 'status'">
          <a-tag :color="productService.getStatusColor(record.status)">
            {{ productService.formatStatus(record.status) }}
          </a-tag>
        </template>

        <!-- 操作列 -->
        <template v-if="column.dataIndex === 'action'">
          <a-space>
            <a v-privilege="'product:view'" @click="productService.openDetailView(record)">
              <EyeOutlined />
              查看
            </a>
            <a v-privilege="'product:edit'" @click="productService.openEditForm(record)">
              <EditOutlined />
              编辑
            </a>
            <!-- <a v-privilege="'product:add'" @click="productService.openAddSkuForm(record.id)">
              <PlusOutlined />
              添加SKU
            </a> -->
            <a v-privilege="'product:delete'" class="danger-link" @click="productService.deleteEntity(record)">
              <DeleteOutlined />
              删除
            </a>
          </a-space>
        </template>
      </template>
    </ax-table-expand>
    <Pagination />
  </a-card>
  <!-- 导入弹窗 -->
  <ImportExcelModal />
  <!-- 商品表单 -->
  <ProductForm />
  <!-- 商品详情 -->
  <ProductDetail />
  <!-- SKU表单 -->
  <SkuForm />
</template>

<style scoped>
.ml-2 {
  margin-left: 8px;
}

.sku-table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.sku-table-title {
  font-weight: bold;
  font-size: 14px;
}
</style>
