<script lang="ts" setup>
import { AxTableOperateButtons } from '@/components/framework-new/AxTable'
import { ButtonGroup } from '@/components/framework-new/Button'
import { Card } from '@/components/framework-new/Card'
import {
  CustomIconAnchor as IconAnchor,
  CustomIconButton as IconButton,
} from '@/components/framework-new/CustomIcon'
import { FlexRow } from '@/components/framework-new/Flex'
import { Form, FormItem, FormRow } from '@/components/framework-new/Form'
import { InputText } from '@/components/framework-new/Input'
import ImportExcelModal from '@/components/framework/ax-import-excel-modal/index.vue'
import Pagination from '@/components/framework/ax-pagination/index.vue'
import Table from '@/components/framework/ax-table/index.vue'
import { DictSelect } from '@/components/support-new/Dict'
import TableOperator from '@/components/support/table-operator/index.vue'
import { TABLE_ID_CONST } from '@/constants/support/table-id-const'

import OrderCreateModal from './components/OrderCreateModal.vue'
import OrderDetail from './components/OrderDetail.vue'
import OrderForm from './components/OrderForm.vue'
import { orderService } from './service/OrderService'

// 提供 service
orderService.provide()

// 注意：现在使用依赖注入方式，不需要手动设置组件引用
</script>

<template>
  <!---------- 查询参数 begin ----------->
  <Form v-privilege="'order:query'">
    <FormRow>
      <FormItem label="订单编号">
        <InputText v-model="orderService.queryParam.orderNo" placeholder="请输入订单编号" />
      </FormItem>
      <FormItem label="客户名称">
        <InputText v-model="orderService.queryParam.customerName" placeholder="请输入客户名称" />
      </FormItem>
      <FormItem label="开单方式">
        <DictSelect
          v-model:value="orderService.queryParam.orderType" key-code="order_type"
          placeholder="请选择开单方式" width="150px"
        />
      </FormItem>
      <FormItem label="订单状态">
        <DictSelect
          v-model:value="orderService.queryParam.orderStatus" key-code="order_status"
          placeholder="请选择订单状态" width="150px"
        />
      </FormItem>
      <FormItem label="付款状态">
        <DictSelect
          v-model:value="orderService.queryParam.paymentStatus" key-code="payment_status"
          placeholder="请选择付款状态" width="150px"
        />
      </FormItem>
      <FormItem label="订单来源">
        <DictSelect
          v-model:value="orderService.queryParam.orderSource" key-code="order_source"
          placeholder="请选择订单来源" width="150px"
        />
      </FormItem>
      <FormItem label="优先级">
        <DictSelect
          v-model:value="orderService.queryParam.priorityLevel" key-code="priority_level"
          placeholder="请选择优先级" width="150px"
        />
      </FormItem>
      <FormItem label="销售员">
        <InputText v-model="orderService.queryParam.salesPersonName" placeholder="请输入销售员姓名" />
      </FormItem>
      <FormItem>
        <ButtonGroup>
          <IconButton label="查询" icon-type="SearchOutlined" type="primary" @click="orderService.onSearch" />
          <IconButton label="重置" icon-type="ReloadOutlined" @click="orderService.resetQuery" />
        </ButtonGroup>
      </FormItem>
    </FormRow>
  </Form>
  <!---------- 查询参数 end ----------->
  <Card>
    <!---------- 表格操作行 begin ----------->
    <FlexRow>
      <FlexRow>
        <IconButton
          v-privilege="'order:add'"
          label="开单"
          icon-type="PlusOutlined"
          type="primary"
          @click="orderService.openCreateModal"
        />
        <AxTableOperateButtons add-config="order:add" batch-delete-config="order:delete" import-config="order:import" export-config="order:export" />
      </FlexRow>
      <FlexRow>
        <TableOperator
          v-model="orderService.columns" :table-id="TABLE_ID_CONST.BUSINESS.ERP.ORDER"
          :refresh="orderService.queryPage"
        />
      </FlexRow>
    </FlexRow>
    <!---------- 表格操作行 end ----------->
    <br>
    <!-- 使用组件化的表格 -->
    <Table>
      <template #bodyCell="{ column, record }">
        <!-- 只为操作列添加模板插槽 -->
        <template v-if="column.dataIndex === 'action'">
          <FlexRow justify="start">
            <IconAnchor
              v-privilege="'order:view'" label="查看" icon-type="EyeOutlined"
              @click="orderService.openDetailView(record)"
            />
            <IconAnchor
              v-privilege="'order:edit'" label="编辑" icon-type="EditOutlined"
              @click="orderService.openEditForm(record)"
            />
            <IconAnchor
              v-privilege="'order:delete'" label="删除" icon-type="DeleteOutlined" color="red"
              @click="orderService.deleteEntity(record)"
            />
          </FlexRow>
        </template>
      </template>
    </Table>
    <Pagination />
  </Card>
  <!-- 导入弹窗 -->
  <ImportExcelModal />
  <!-- 开单弹窗 -->
  <OrderCreateModal />
  <!-- 订单表单 -->
  <OrderForm />
  <!-- 订单详情 -->
  <OrderDetail />
</template>
