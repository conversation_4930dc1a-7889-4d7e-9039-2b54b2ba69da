type Key = string | number

export interface OperateParams {
  addConfig?: string
  batchDeleteConfig?: string
  importConfig?: string
  exportConfig?: string
}

export interface OperateService {
  selectedRowKeyList: (string | number)[]
  openAddForm: () => void
  confirmBatchDeleteEntities: () => void
  showImportModal: () => void
  onExport: () => void
}
export interface AxTableService {
  columns: Record<string, unknown>[]
  getRawTableData: () => unknown[]
  tableLoading: boolean
  rowKey: string
  scroll?: object
  onChange?: (...args: unknown[]) => void
  selectedRowKeyList: Key[]
  onSelectChange: (selectedRowKeys: Key[], selectedRows: unknown[]) => void
}
