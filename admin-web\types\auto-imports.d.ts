/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// noinspection JSUnusedGlobalSymbols
// Generated by unplugin-auto-import
// biome-ignore lint: disable
export {}
declare global {
  const EffectScope: typeof import('vue')['EffectScope']
  const _add: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['add']
  const _after: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['after']
  const _ary: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['ary']
  const _assign: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['assign']
  const _assignIn: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['assignIn']
  const _assignInWith: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['assignInWith']
  const _assignWith: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['assignWith']
  const _at: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['at']
  const _attempt: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['attempt']
  const _before: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['before']
  const _bind: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['bind']
  const _bindAll: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['bindAll']
  const _bindKey: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['bindKey']
  const _camelCase: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['camelCase']
  const _capitalize: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['capitalize']
  const _castArray: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['castArray']
  const _ceil: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['ceil']
  const _chain: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['chain']
  const _chunk: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['chunk']
  const _clamp: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['clamp']
  const _clone: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['clone']
  const _cloneDeep: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['cloneDeep']
  const _cloneDeepWith: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['cloneDeepWith']
  const _cloneWith: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['cloneWith']
  const _compact: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['compact']
  const _concat: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['concat']
  const _cond: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['cond']
  const _conforms: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['conforms']
  const _conformsTo: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['conformsTo']
  const _constant: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['constant']
  const _countBy: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['countBy']
  const _create: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['create']
  const _curry: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['curry']
  const _curryRight: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['curryRight']
  const _debounce: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['debounce']
  const _deburr: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['deburr']
  const _defaultTo: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['defaultTo']
  const _defaults: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['defaults']
  const _defaultsDeep: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['defaultsDeep']
  const _defer: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['defer']
  const _delay: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['delay']
  const _difference: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['difference']
  const _differenceBy: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['differenceBy']
  const _differenceWith: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['differenceWith']
  const _divide: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['divide']
  const _drop: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['drop']
  const _dropRight: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['dropRight']
  const _dropRightWhile: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['dropRightWhile']
  const _dropWhile: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['dropWhile']
  const _each: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['each']
  const _eachRight: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['eachRight']
  const _endsWith: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['endsWith']
  const _entries: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['entries']
  const _entriesIn: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['entriesIn']
  const _eq: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['eq']
  const _escape: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['escape']
  const _escapeRegExp: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['escapeRegExp']
  const _every: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['every']
  const _extend: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['extend']
  const _extendWith: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['extendWith']
  const _fill: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['fill']
  const _filter: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['filter']
  const _find: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['find']
  const _findIndex: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['findIndex']
  const _findKey: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['findKey']
  const _findLast: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['findLast']
  const _findLastIndex: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['findLastIndex']
  const _findLastKey: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['findLastKey']
  const _first: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['first']
  const _flatMap: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['flatMap']
  const _flatMapDeep: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['flatMapDeep']
  const _flatMapDepth: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['flatMapDepth']
  const _flatten: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['flatten']
  const _flattenDeep: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['flattenDeep']
  const _flattenDepth: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['flattenDepth']
  const _flip: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['flip']
  const _floor: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['floor']
  const _flow: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['flow']
  const _flowRight: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['flowRight']
  const _forEach: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['forEach']
  const _forEachRight: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['forEachRight']
  const _forIn: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['forIn']
  const _forInRight: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['forInRight']
  const _forOwn: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['forOwn']
  const _forOwnRight: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['forOwnRight']
  const _fromPairs: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['fromPairs']
  const _functions: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['functions']
  const _functionsIn: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['functionsIn']
  const _get: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['get']
  const _groupBy: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['groupBy']
  const _gt: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['gt']
  const _gte: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['gte']
  const _has: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['has']
  const _hasIn: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['hasIn']
  const _identity: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['identity']
  const _inRange: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['inRange']
  const _includes: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['includes']
  const _indexOf: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['indexOf']
  const _initial: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['initial']
  const _intersection: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['intersection']
  const _intersectionBy: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['intersectionBy']
  const _intersectionWith: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['intersectionWith']
  const _invert: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['invert']
  const _invertBy: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['invertBy']
  const _invoke: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['invoke']
  const _invokeMap: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['invokeMap']
  const _isArguments: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['isArguments']
  const _isArray: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['isArray']
  const _isArrayBuffer: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['isArrayBuffer']
  const _isArrayLike: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['isArrayLike']
  const _isArrayLikeObject: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['isArrayLikeObject']
  const _isBoolean: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['isBoolean']
  const _isBuffer: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['isBuffer']
  const _isDate: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['isDate']
  const _isElement: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['isElement']
  const _isEmpty: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['isEmpty']
  const _isEqual: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['isEqual']
  const _isEqualWith: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['isEqualWith']
  const _isError: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['isError']
  const _isFinite: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['isFinite']
  const _isFunction: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['isFunction']
  const _isInteger: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['isInteger']
  const _isLength: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['isLength']
  const _isMap: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['isMap']
  const _isMatch: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['isMatch']
  const _isMatchWith: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['isMatchWith']
  const _isNaN: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['isNaN']
  const _isNative: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['isNative']
  const _isNil: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['isNil']
  const _isNull: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['isNull']
  const _isNumber: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['isNumber']
  const _isObject: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['isObject']
  const _isObjectLike: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['isObjectLike']
  const _isPlainObject: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['isPlainObject']
  const _isRegExp: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['isRegExp']
  const _isSafeInteger: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['isSafeInteger']
  const _isSet: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['isSet']
  const _isString: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['isString']
  const _isSymbol: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['isSymbol']
  const _isTypedArray: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['isTypedArray']
  const _isUndefined: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['isUndefined']
  const _isWeakMap: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['isWeakMap']
  const _isWeakSet: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['isWeakSet']
  const _iteratee: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['iteratee']
  const _join: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['join']
  const _kebabCase: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['kebabCase']
  const _keyBy: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['keyBy']
  const _keys: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['keys']
  const _keysIn: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['keysIn']
  const _last: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['last']
  const _lastIndexOf: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['lastIndexOf']
  const _lowerCase: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['lowerCase']
  const _lowerFirst: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['lowerFirst']
  const _lt: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['lt']
  const _lte: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['lte']
  const _map: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['map']
  const _mapKeys: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['mapKeys']
  const _mapValues: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['mapValues']
  const _matches: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['matches']
  const _matchesProperty: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['matchesProperty']
  const _max: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['max']
  const _maxBy: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['maxBy']
  const _mean: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['mean']
  const _meanBy: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['meanBy']
  const _memoize: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['memoize']
  const _merge: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['merge']
  const _mergeWith: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['mergeWith']
  const _method: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['method']
  const _methodOf: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['methodOf']
  const _min: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['min']
  const _minBy: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['minBy']
  const _mixin: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['mixin']
  const _multiply: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['multiply']
  const _negate: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['negate']
  const _noop: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['noop']
  const _now: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['now']
  const _nth: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['nth']
  const _nthArg: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['nthArg']
  const _omit: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['omit']
  const _omitBy: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['omitBy']
  const _once: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['once']
  const _orderBy: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['orderBy']
  const _over: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['over']
  const _overArgs: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['overArgs']
  const _overEvery: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['overEvery']
  const _overSome: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['overSome']
  const _pad: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['pad']
  const _padEnd: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['padEnd']
  const _padStart: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['padStart']
  const _parseInt: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['parseInt']
  const _partial: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['partial']
  const _partialRight: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['partialRight']
  const _partition: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['partition']
  const _pick: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['pick']
  const _pickBy: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['pickBy']
  const _property: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['property']
  const _propertyOf: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['propertyOf']
  const _pull: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['pull']
  const _pullAll: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['pullAll']
  const _pullAllBy: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['pullAllBy']
  const _pullAllWith: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['pullAllWith']
  const _pullAt: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['pullAt']
  const _random: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['random']
  const _range: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['range']
  const _rangeRight: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['rangeRight']
  const _rearg: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['rearg']
  const _reduce: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['reduce']
  const _reduceRight: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['reduceRight']
  const _reject: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['reject']
  const _remove: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['remove']
  const _repeat: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['repeat']
  const _replace: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['replace']
  const _rest: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['rest']
  const _result: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['result']
  const _reverse: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['reverse']
  const _round: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['round']
  const _sample: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['sample']
  const _sampleSize: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['sampleSize']
  const _set: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['set']
  const _setWith: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['setWith']
  const _shuffle: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['shuffle']
  const _size: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['size']
  const _slice: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['slice']
  const _snakeCase: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['snakeCase']
  const _some: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['some']
  const _sortBy: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['sortBy']
  const _sortedIndex: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['sortedIndex']
  const _sortedIndexBy: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['sortedIndexBy']
  const _sortedIndexOf: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['sortedIndexOf']
  const _sortedLastIndex: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['sortedLastIndex']
  const _sortedLastIndexBy: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['sortedLastIndexBy']
  const _sortedLastIndexOf: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['sortedLastIndexOf']
  const _sortedUniq: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['sortedUniq']
  const _sortedUniqBy: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['sortedUniqBy']
  const _split: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['split']
  const _spread: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['spread']
  const _startCase: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['startCase']
  const _startsWith: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['startsWith']
  const _stubArray: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['stubArray']
  const _stubFalse: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['stubFalse']
  const _stubObject: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['stubObject']
  const _stubString: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['stubString']
  const _stubTrue: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['stubTrue']
  const _subtract: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['subtract']
  const _sum: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['sum']
  const _sumBy: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['sumBy']
  const _tail: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['tail']
  const _take: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['take']
  const _takeRight: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['takeRight']
  const _takeRightWhile: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['takeRightWhile']
  const _takeWhile: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['takeWhile']
  const _tap: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['tap']
  const _template: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['template']
  const _throttle: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['throttle']
  const _times: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['times']
  const _toArray: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['toArray']
  const _toFinite: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['toFinite']
  const _toInteger: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['toInteger']
  const _toLength: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['toLength']
  const _toLower: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['toLower']
  const _toNumber: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['toNumber']
  const _toPairs: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['toPairs']
  const _toPairsIn: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['toPairsIn']
  const _toPath: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['toPath']
  const _toPlainObject: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['toPlainObject']
  const _toSafeInteger: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['toSafeInteger']
  const _toString: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['toString']
  const _toUpper: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['toUpper']
  const _transform: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['transform']
  const _trim: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['trim']
  const _trimEnd: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['trimEnd']
  const _trimStart: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['trimStart']
  const _truncate: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['truncate']
  const _unary: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['unary']
  const _unescape: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['unescape']
  const _union: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['union']
  const _unionBy: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['unionBy']
  const _unionWith: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['unionWith']
  const _uniq: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['uniq']
  const _uniqBy: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['uniqBy']
  const _uniqWith: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['uniqWith']
  const _uniqueId: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['uniqueId']
  const _unset: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['unset']
  const _unzip: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['unzip']
  const _unzipWith: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['unzipWith']
  const _update: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['update']
  const _updateWith: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['updateWith']
  const _upperCase: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['upperCase']
  const _upperFirst: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['upperFirst']
  const _values: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['values']
  const _valuesIn: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['valuesIn']
  const _without: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['without']
  const _words: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['words']
  const _wrap: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['wrap']
  const _xor: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['xor']
  const _xorBy: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['xorBy']
  const _xorWith: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['xorWith']
  const _zip: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['zip']
  const _zipObject: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['zipObject']
  const _zipObjectDeep: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['zipObjectDeep']
  const _zipWith: typeof import('../node_modules/lodash-imports/dist/lodash.mjs')['zipWith']
  const asyncComputed: typeof import('@vueuse/core')['asyncComputed']
  const autoResetRef: typeof import('@vueuse/core')['autoResetRef']
  const cacheGetDictValueList: typeof import('../src/utils/dict-util')['cacheGetDictValueList']
  const computed: typeof import('vue')['computed']
  const computedAsync: typeof import('@vueuse/core')['computedAsync']
  const computedEager: typeof import('@vueuse/core')['computedEager']
  const computedInject: typeof import('@vueuse/core')['computedInject']
  const computedWithControl: typeof import('@vueuse/core')['computedWithControl']
  const controlledComputed: typeof import('@vueuse/core')['controlledComputed']
  const controlledRef: typeof import('@vueuse/core')['controlledRef']
  const convertLowerCamel: typeof import('../src/utils/str-util')['convertLowerCamel']
  const convertLowerHyphen: typeof import('../src/utils/str-util')['convertLowerHyphen']
  const convertUpperCamel: typeof import('../src/utils/str-util')['convertUpperCamel']
  const createApp: typeof import('vue')['createApp']
  const createDictConfig: typeof import('../src/utils/dict-util')['createDictConfig']
  const createEventHook: typeof import('@vueuse/core')['createEventHook']
  const createGlobalState: typeof import('@vueuse/core')['createGlobalState']
  const createInjectionState: typeof import('@vueuse/core')['createInjectionState']
  const createReactiveFn: typeof import('@vueuse/core')['createReactiveFn']
  const createRef: typeof import('@vueuse/core')['createRef']
  const createReusableTemplate: typeof import('@vueuse/core')['createReusableTemplate']
  const createSharedComposable: typeof import('@vueuse/core')['createSharedComposable']
  const createTemplatePromise: typeof import('@vueuse/core')['createTemplatePromise']
  const createUnrefFn: typeof import('@vueuse/core')['createUnrefFn']
  const customRef: typeof import('vue')['customRef']
  const dayjs: typeof import('@/plugins/dayjs')['dayjs']
  const debouncedRef: typeof import('@vueuse/core')['debouncedRef']
  const debouncedWatch: typeof import('@vueuse/core')['debouncedWatch']
  const defineAsyncComponent: typeof import('vue')['defineAsyncComponent']
  const defineComponent: typeof import('vue')['defineComponent']
  const eagerComputed: typeof import('@vueuse/core')['eagerComputed']
  const effectScope: typeof import('vue')['effectScope']
  const extendRef: typeof import('@vueuse/core')['extendRef']
  const faker: typeof import('@faker-js/faker')['fakerZH_CN']
  const formatDate: typeof import('../src/utils/format')['formatDate']
  const formatDateTime: typeof import('../src/utils/format')['formatDateTime']
  const getCurrentInstance: typeof import('vue')['getCurrentInstance']
  const getCurrentScope: typeof import('vue')['getCurrentScope']
  const getDictItem: typeof import('../src/utils/dict-util')['getDictItem']
  const getDictLabel: typeof import('../src/utils/dict-util')['getDictLabel']
  const getDictOptions: typeof import('../src/utils/dict-util')['getDictOptions']
  const getInitializedLanguage: typeof import('../src/store/modules/system/app-config')['getInitializedLanguage']
  const h: typeof import('vue')['h']
  const ignorableWatch: typeof import('@vueuse/core')['ignorableWatch']
  const inject: typeof import('vue')['inject']
  const injectLocal: typeof import('@vueuse/core')['injectLocal']
  const isDefined: typeof import('@vueuse/core')['isDefined']
  const isProxy: typeof import('vue')['isProxy']
  const isReactive: typeof import('vue')['isReactive']
  const isReadonly: typeof import('vue')['isReadonly']
  const isRef: typeof import('vue')['isRef']
  const localClear: typeof import('../src/utils/local-util')['localClear']
  const localRead: typeof import('../src/utils/local-util')['localRead']
  const localRemove: typeof import('../src/utils/local-util')['localRemove']
  const localSave: typeof import('../src/utils/local-util')['localSave']
  const makeDestructurable: typeof import('@vueuse/core')['makeDestructurable']
  const markRaw: typeof import('vue')['markRaw']
  const nextTick: typeof import('vue')['nextTick']
  const onActivated: typeof import('vue')['onActivated']
  const onBeforeMount: typeof import('vue')['onBeforeMount']
  const onBeforeRouteLeave: typeof import('vue-router')['onBeforeRouteLeave']
  const onBeforeRouteUpdate: typeof import('vue-router')['onBeforeRouteUpdate']
  const onBeforeUnmount: typeof import('vue')['onBeforeUnmount']
  const onBeforeUpdate: typeof import('vue')['onBeforeUpdate']
  const onClickOutside: typeof import('@vueuse/core')['onClickOutside']
  const onDeactivated: typeof import('vue')['onDeactivated']
  const onElementRemoval: typeof import('@vueuse/core')['onElementRemoval']
  const onErrorCaptured: typeof import('vue')['onErrorCaptured']
  const onKeyStroke: typeof import('@vueuse/core')['onKeyStroke']
  const onLongPress: typeof import('@vueuse/core')['onLongPress']
  const onMounted: typeof import('vue')['onMounted']
  const onRenderTracked: typeof import('vue')['onRenderTracked']
  const onRenderTriggered: typeof import('vue')['onRenderTriggered']
  const onScopeDispose: typeof import('vue')['onScopeDispose']
  const onServerPrefetch: typeof import('vue')['onServerPrefetch']
  const onStartTyping: typeof import('@vueuse/core')['onStartTyping']
  const onUnmounted: typeof import('vue')['onUnmounted']
  const onUpdated: typeof import('vue')['onUpdated']
  const onWatcherCleanup: typeof import('vue')['onWatcherCleanup']
  const pausableWatch: typeof import('@vueuse/core')['pausableWatch']
  const provide: typeof import('vue')['provide']
  const provideLocal: typeof import('@vueuse/core')['provideLocal']
  const reactify: typeof import('@vueuse/core')['reactify']
  const reactifyObject: typeof import('@vueuse/core')['reactifyObject']
  const reactive: typeof import('vue')['reactive']
  const reactiveComputed: typeof import('@vueuse/core')['reactiveComputed']
  const reactiveOmit: typeof import('@vueuse/core')['reactiveOmit']
  const reactivePick: typeof import('@vueuse/core')['reactivePick']
  const readonly: typeof import('vue')['readonly']
  const ref: typeof import('vue')['ref']
  const refAutoReset: typeof import('@vueuse/core')['refAutoReset']
  const refDebounced: typeof import('@vueuse/core')['refDebounced']
  const refDefault: typeof import('@vueuse/core')['refDefault']
  const refThrottled: typeof import('@vueuse/core')['refThrottled']
  const refWithControl: typeof import('@vueuse/core')['refWithControl']
  const registerEcharts: typeof import('../src/utils/registerEcharts')['registerEcharts']
  const resolveComponent: typeof import('vue')['resolveComponent']
  const resolveRef: typeof import('@vueuse/core')['resolveRef']
  const resolveUnref: typeof import('@vueuse/core')['resolveUnref']
  const shallowReactive: typeof import('vue')['shallowReactive']
  const shallowReadonly: typeof import('vue')['shallowReadonly']
  const shallowRef: typeof import('vue')['shallowRef']
  const store: typeof import('../src/store/index')['store']
  const syncRef: typeof import('@vueuse/core')['syncRef']
  const syncRefs: typeof import('@vueuse/core')['syncRefs']
  const templateRef: typeof import('@vueuse/core')['templateRef']
  const throttledRef: typeof import('@vueuse/core')['throttledRef']
  const throttledWatch: typeof import('@vueuse/core')['throttledWatch']
  const toRaw: typeof import('vue')['toRaw']
  const toReactive: typeof import('@vueuse/core')['toReactive']
  const toRef: typeof import('vue')['toRef']
  const toRefs: typeof import('vue')['toRefs']
  const toValue: typeof import('vue')['toValue']
  const triggerRef: typeof import('vue')['triggerRef']
  const tryOnBeforeMount: typeof import('@vueuse/core')['tryOnBeforeMount']
  const tryOnBeforeUnmount: typeof import('@vueuse/core')['tryOnBeforeUnmount']
  const tryOnMounted: typeof import('@vueuse/core')['tryOnMounted']
  const tryOnScopeDispose: typeof import('@vueuse/core')['tryOnScopeDispose']
  const tryOnUnmounted: typeof import('@vueuse/core')['tryOnUnmounted']
  const unref: typeof import('vue')['unref']
  const unrefElement: typeof import('@vueuse/core')['unrefElement']
  const until: typeof import('@vueuse/core')['until']
  const useActiveElement: typeof import('@vueuse/core')['useActiveElement']
  const useAnimate: typeof import('@vueuse/core')['useAnimate']
  const useAppConfigStore: typeof import('../src/store/modules/system/app-config')['useAppConfigStore']
  const useArrayDifference: typeof import('@vueuse/core')['useArrayDifference']
  const useArrayEvery: typeof import('@vueuse/core')['useArrayEvery']
  const useArrayFilter: typeof import('@vueuse/core')['useArrayFilter']
  const useArrayFind: typeof import('@vueuse/core')['useArrayFind']
  const useArrayFindIndex: typeof import('@vueuse/core')['useArrayFindIndex']
  const useArrayFindLast: typeof import('@vueuse/core')['useArrayFindLast']
  const useArrayIncludes: typeof import('@vueuse/core')['useArrayIncludes']
  const useArrayJoin: typeof import('@vueuse/core')['useArrayJoin']
  const useArrayMap: typeof import('@vueuse/core')['useArrayMap']
  const useArrayReduce: typeof import('@vueuse/core')['useArrayReduce']
  const useArraySome: typeof import('@vueuse/core')['useArraySome']
  const useArrayUnique: typeof import('@vueuse/core')['useArrayUnique']
  const useAsyncQueue: typeof import('@vueuse/core')['useAsyncQueue']
  const useAsyncState: typeof import('@vueuse/core')['useAsyncState']
  const useAttrs: typeof import('vue')['useAttrs']
  const useBase64: typeof import('@vueuse/core')['useBase64']
  const useBattery: typeof import('@vueuse/core')['useBattery']
  const useBluetooth: typeof import('@vueuse/core')['useBluetooth']
  const useBreakpoints: typeof import('@vueuse/core')['useBreakpoints']
  const useBroadcastChannel: typeof import('@vueuse/core')['useBroadcastChannel']
  const useBrowserLocation: typeof import('@vueuse/core')['useBrowserLocation']
  const useCached: typeof import('@vueuse/core')['useCached']
  const useClipboard: typeof import('@vueuse/core')['useClipboard']
  const useClipboardItems: typeof import('@vueuse/core')['useClipboardItems']
  const useCloned: typeof import('@vueuse/core')['useCloned']
  const useColorMode: typeof import('@vueuse/core')['useColorMode']
  const useConfirmDialog: typeof import('@vueuse/core')['useConfirmDialog']
  const useCountdown: typeof import('@vueuse/core')['useCountdown']
  const useCounter: typeof import('@vueuse/core')['useCounter']
  const useCssModule: typeof import('vue')['useCssModule']
  const useCssVar: typeof import('@vueuse/core')['useCssVar']
  const useCssVars: typeof import('vue')['useCssVars']
  const useCurrentElement: typeof import('@vueuse/core')['useCurrentElement']
  const useCycleList: typeof import('@vueuse/core')['useCycleList']
  const useDark: typeof import('@vueuse/core')['useDark']
  const useDateFormat: typeof import('@vueuse/core')['useDateFormat']
  const useDebounce: typeof import('@vueuse/core')['useDebounce']
  const useDebounceFn: typeof import('@vueuse/core')['useDebounceFn']
  const useDebouncedRefHistory: typeof import('@vueuse/core')['useDebouncedRefHistory']
  const useDeviceMotion: typeof import('@vueuse/core')['useDeviceMotion']
  const useDeviceOrientation: typeof import('@vueuse/core')['useDeviceOrientation']
  const useDevicePixelRatio: typeof import('@vueuse/core')['useDevicePixelRatio']
  const useDevicesList: typeof import('@vueuse/core')['useDevicesList']
  const useDisplayMedia: typeof import('@vueuse/core')['useDisplayMedia']
  const useDocumentVisibility: typeof import('@vueuse/core')['useDocumentVisibility']
  const useDraggable: typeof import('@vueuse/core')['useDraggable']
  const useDropZone: typeof import('@vueuse/core')['useDropZone']
  const useElementBounding: typeof import('@vueuse/core')['useElementBounding']
  const useElementByPoint: typeof import('@vueuse/core')['useElementByPoint']
  const useElementHover: typeof import('@vueuse/core')['useElementHover']
  const useElementSize: typeof import('@vueuse/core')['useElementSize']
  const useElementVisibility: typeof import('@vueuse/core')['useElementVisibility']
  const useEventBus: typeof import('@vueuse/core')['useEventBus']
  const useEventListener: typeof import('@vueuse/core')['useEventListener']
  const useEventSource: typeof import('@vueuse/core')['useEventSource']
  const useEyeDropper: typeof import('@vueuse/core')['useEyeDropper']
  const useFavicon: typeof import('@vueuse/core')['useFavicon']
  const useFetch: typeof import('@vueuse/core')['useFetch']
  const useFileDialog: typeof import('@vueuse/core')['useFileDialog']
  const useFileSystemAccess: typeof import('@vueuse/core')['useFileSystemAccess']
  const useFocus: typeof import('@vueuse/core')['useFocus']
  const useFocusWithin: typeof import('@vueuse/core')['useFocusWithin']
  const useFps: typeof import('@vueuse/core')['useFps']
  const useFullscreen: typeof import('@vueuse/core')['useFullscreen']
  const useGamepad: typeof import('@vueuse/core')['useGamepad']
  const useGeolocation: typeof import('@vueuse/core')['useGeolocation']
  const useId: typeof import('vue')['useId']
  const useIdle: typeof import('@vueuse/core')['useIdle']
  const useImage: typeof import('@vueuse/core')['useImage']
  const useInfiniteScroll: typeof import('@vueuse/core')['useInfiniteScroll']
  const useIntersectionObserver: typeof import('@vueuse/core')['useIntersectionObserver']
  const useInterval: typeof import('@vueuse/core')['useInterval']
  const useIntervalFn: typeof import('@vueuse/core')['useIntervalFn']
  const useKeyModifier: typeof import('@vueuse/core')['useKeyModifier']
  const useLastChanged: typeof import('@vueuse/core')['useLastChanged']
  const useLink: typeof import('vue-router')['useLink']
  const useLoading: typeof import('vue-loading-overlay')['useLoading']
  const useLocalStorage: typeof import('@vueuse/core')['useLocalStorage']
  const useMagicKeys: typeof import('@vueuse/core')['useMagicKeys']
  const useManualRefHistory: typeof import('@vueuse/core')['useManualRefHistory']
  const useMediaControls: typeof import('@vueuse/core')['useMediaControls']
  const useMediaQuery: typeof import('@vueuse/core')['useMediaQuery']
  const useMemoize: typeof import('@vueuse/core')['useMemoize']
  const useMemory: typeof import('@vueuse/core')['useMemory']
  const useModel: typeof import('vue')['useModel']
  const useMounted: typeof import('@vueuse/core')['useMounted']
  const useMouse: typeof import('@vueuse/core')['useMouse']
  const useMouseInElement: typeof import('@vueuse/core')['useMouseInElement']
  const useMousePressed: typeof import('@vueuse/core')['useMousePressed']
  const useMutationObserver: typeof import('@vueuse/core')['useMutationObserver']
  const useNavigatorLanguage: typeof import('@vueuse/core')['useNavigatorLanguage']
  const useNetwork: typeof import('@vueuse/core')['useNetwork']
  const useNow: typeof import('@vueuse/core')['useNow']
  const useObjectUrl: typeof import('@vueuse/core')['useObjectUrl']
  const useOffsetPagination: typeof import('@vueuse/core')['useOffsetPagination']
  const useOnline: typeof import('@vueuse/core')['useOnline']
  const usePageLeave: typeof import('@vueuse/core')['usePageLeave']
  const useParallax: typeof import('@vueuse/core')['useParallax']
  const useParentElement: typeof import('@vueuse/core')['useParentElement']
  const usePerformanceObserver: typeof import('@vueuse/core')['usePerformanceObserver']
  const usePermission: typeof import('@vueuse/core')['usePermission']
  const usePointer: typeof import('@vueuse/core')['usePointer']
  const usePointerLock: typeof import('@vueuse/core')['usePointerLock']
  const usePointerSwipe: typeof import('@vueuse/core')['usePointerSwipe']
  const usePreferredColorScheme: typeof import('@vueuse/core')['usePreferredColorScheme']
  const usePreferredContrast: typeof import('@vueuse/core')['usePreferredContrast']
  const usePreferredDark: typeof import('@vueuse/core')['usePreferredDark']
  const usePreferredLanguages: typeof import('@vueuse/core')['usePreferredLanguages']
  const usePreferredReducedMotion: typeof import('@vueuse/core')['usePreferredReducedMotion']
  const usePreferredReducedTransparency: typeof import('@vueuse/core')['usePreferredReducedTransparency']
  const usePrevious: typeof import('@vueuse/core')['usePrevious']
  const useRafFn: typeof import('@vueuse/core')['useRafFn']
  const useRefHistory: typeof import('@vueuse/core')['useRefHistory']
  const useResizeObserver: typeof import('@vueuse/core')['useResizeObserver']
  const useRoleStore: typeof import('../src/store/modules/system/role')['useRoleStore']
  const useRoute: typeof import('vue-router')['useRoute']
  const useRouter: typeof import('vue-router')['useRouter']
  const useSSRWidth: typeof import('@vueuse/core')['useSSRWidth']
  const useScreenOrientation: typeof import('@vueuse/core')['useScreenOrientation']
  const useScreenSafeArea: typeof import('@vueuse/core')['useScreenSafeArea']
  const useScriptTag: typeof import('@vueuse/core')['useScriptTag']
  const useScroll: typeof import('@vueuse/core')['useScroll']
  const useScrollLock: typeof import('@vueuse/core')['useScrollLock']
  const useSessionStorage: typeof import('@vueuse/core')['useSessionStorage']
  const useShare: typeof import('@vueuse/core')['useShare']
  const useSlots: typeof import('vue')['useSlots']
  const useSorted: typeof import('@vueuse/core')['useSorted']
  const useSpeechRecognition: typeof import('@vueuse/core')['useSpeechRecognition']
  const useSpeechSynthesis: typeof import('@vueuse/core')['useSpeechSynthesis']
  const useSpinStore: typeof import('../src/store/modules/system/spin')['useSpinStore']
  const useStepper: typeof import('@vueuse/core')['useStepper']
  const useStorage: typeof import('@vueuse/core')['useStorage']
  const useStorageAsync: typeof import('@vueuse/core')['useStorageAsync']
  const useStyleTag: typeof import('@vueuse/core')['useStyleTag']
  const useSupported: typeof import('@vueuse/core')['useSupported']
  const useSwipe: typeof import('@vueuse/core')['useSwipe']
  const useTemplateRef: typeof import('vue')['useTemplateRef']
  const useTemplateRefsList: typeof import('@vueuse/core')['useTemplateRefsList']
  const useTextDirection: typeof import('@vueuse/core')['useTextDirection']
  const useTextSelection: typeof import('@vueuse/core')['useTextSelection']
  const useTextareaAutosize: typeof import('@vueuse/core')['useTextareaAutosize']
  const useThrottle: typeof import('@vueuse/core')['useThrottle']
  const useThrottleFn: typeof import('@vueuse/core')['useThrottleFn']
  const useThrottledRefHistory: typeof import('@vueuse/core')['useThrottledRefHistory']
  const useTimeAgo: typeof import('@vueuse/core')['useTimeAgo']
  const useTimeout: typeof import('@vueuse/core')['useTimeout']
  const useTimeoutFn: typeof import('@vueuse/core')['useTimeoutFn']
  const useTimeoutPoll: typeof import('@vueuse/core')['useTimeoutPoll']
  const useTimestamp: typeof import('@vueuse/core')['useTimestamp']
  const useTitle: typeof import('@vueuse/core')['useTitle']
  const useToNumber: typeof import('@vueuse/core')['useToNumber']
  const useToString: typeof import('@vueuse/core')['useToString']
  const useToggle: typeof import('@vueuse/core')['useToggle']
  const useTransition: typeof import('@vueuse/core')['useTransition']
  const useUrlSearchParams: typeof import('@vueuse/core')['useUrlSearchParams']
  const useUserMedia: typeof import('@vueuse/core')['useUserMedia']
  const useUserStore: typeof import('../src/store/modules/system/user')['useUserStore']
  const useVModel: typeof import('@vueuse/core')['useVModel']
  const useVModels: typeof import('@vueuse/core')['useVModels']
  const useVibrate: typeof import('@vueuse/core')['useVibrate']
  const useVirtualList: typeof import('@vueuse/core')['useVirtualList']
  const useWakeLock: typeof import('@vueuse/core')['useWakeLock']
  const useWebNotification: typeof import('@vueuse/core')['useWebNotification']
  const useWebSocket: typeof import('@vueuse/core')['useWebSocket']
  const useWebWorker: typeof import('@vueuse/core')['useWebWorker']
  const useWebWorkerFn: typeof import('@vueuse/core')['useWebWorkerFn']
  const useWindowFocus: typeof import('@vueuse/core')['useWindowFocus']
  const useWindowScroll: typeof import('@vueuse/core')['useWindowScroll']
  const useWindowSize: typeof import('@vueuse/core')['useWindowSize']
  const watch: typeof import('vue')['watch']
  const watchArray: typeof import('@vueuse/core')['watchArray']
  const watchAtMost: typeof import('@vueuse/core')['watchAtMost']
  const watchDebounced: typeof import('@vueuse/core')['watchDebounced']
  const watchDeep: typeof import('@vueuse/core')['watchDeep']
  const watchEffect: typeof import('vue')['watchEffect']
  const watchIgnorable: typeof import('@vueuse/core')['watchIgnorable']
  const watchImmediate: typeof import('@vueuse/core')['watchImmediate']
  const watchOnce: typeof import('@vueuse/core')['watchOnce']
  const watchPausable: typeof import('@vueuse/core')['watchPausable']
  const watchPostEffect: typeof import('vue')['watchPostEffect']
  const watchSyncEffect: typeof import('vue')['watchSyncEffect']
  const watchThrottled: typeof import('@vueuse/core')['watchThrottled']
  const watchTriggerable: typeof import('@vueuse/core')['watchTriggerable']
  const watchWithFilter: typeof import('@vueuse/core')['watchWithFilter']
  const whenever: typeof import('@vueuse/core')['whenever']
}
// for type re-export
declare global {
  // @ts-ignore
  export type { Component, ComponentPublicInstance, ComputedRef, DirectiveBinding, ExtractDefaultPropTypes, ExtractPropTypes, ExtractPublicPropTypes, InjectionKey, PropType, Ref, MaybeRef, MaybeRefOrGetter, VNode, WritableComputedRef } from 'vue'
  import('vue')
  // @ts-ignore
  export type { UserTagNav } from '../src/store/modules/model/UserTagNav'
  import('../src/store/modules/model/UserTagNav')
  // @ts-ignore
  export type { DictItem } from '../src/utils/dict-util'
  import('../src/utils/dict-util')
}
