import type { BaseModel } from '@/api/base-model/base-model'
import type { PageParamModel } from '@/api/base-model/page-model'
import type { BoxConfig, BoxResult } from '@/api/business/box/model/box-form-model'
import type { InjectionKey } from 'vue'
import { BaseCrudService } from '@/service/BaseCrudService'
import { inject, provide, ref } from 'vue'

// BoxSelect服务接口 - 只包含业务相关的属性和方法
export interface BoxSelectService {
  // 对外暴露的数据（直接返回值而不是ComputedRef）
  selectedBox: BoxResult | undefined
  boxConfig: BoxConfig
  onChange: () => void
}

// 注入键
const BOX_SELECT_KEY: InjectionKey<BoxSelectService> = Symbol('box-select-service')

/**
 * 盒子选择器服务类
 * 可以直接实例化使用，也可以通过内部单例使用
 */
export class BoxSelectServiceImp<T extends BaseModel, FormParam extends BaseModel, QueryParam extends { pageParam: PageParamModel }> extends BaseCrudService<T, FormParam, QueryParam> {
  // 私有属性 - 内部使用的ref
  private _selectedBox = ref<BoxResult | undefined>(undefined)

  // 通过getter访问器暴露属性，更优雅
  get selectedBox(): BoxResult | undefined {
    return this._selectedBox.value
  }

  // 通过setter访问器设置属性，与getter成对出现
  set selectedBox(box: BoxResult | undefined) {
    this._selectedBox.value = box
  }

  get boxConfig(): BoxConfig {
    return this._selectedBox.value?.boxConfig || { }
  }

  /**
   * 选项变化时的回调钩子，子类可重写以添加逻辑
   */
  onChange(): void {
  }

  /**
   * 将当前服务实例提供给依赖注入系统
   */
  provide(): void {
    provide(BOX_SELECT_KEY, this)
  }
}

/**
 * 注入盒子选择器服务
 * @param key 注入键，默认使用BOX_SELECT_KEY
 * @returns 注入的服务实例
 */
export function injectBoxSelectService(key: typeof BOX_SELECT_KEY = BOX_SELECT_KEY): BoxSelectService {
  const service = inject(key)
  if (!service) {
    throw new Error('BoxSelectService is not provided. Make sure to call service.provide() in a parent component.')
  }

  return service
}
