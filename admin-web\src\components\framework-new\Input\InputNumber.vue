<script setup lang="ts">
import { defineModel, defineProps } from 'vue'

const {
  allowClear = true,
  placeholder = '请输入',
} = defineProps<{
  placeholder?: string
  allowClear?: boolean
}>()

const modelValue = defineModel<string>({ default: '' })
</script>

<template>
  <a-input
    v-model:value="modelValue"
    v-bind="$attrs"
    class="smart-query-input-text w-48"
    type="number"
    :allow-clear="allowClear"
    :placeholder="placeholder"
  />
</template>
