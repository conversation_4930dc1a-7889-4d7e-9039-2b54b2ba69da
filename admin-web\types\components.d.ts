/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AAlert: typeof import('ant-design-vue/es')['Alert']
    AAvatar: typeof import('ant-design-vue/es')['Avatar']
    ABackTop: typeof import('ant-design-vue/es')['BackTop']
    ABadge: typeof import('ant-design-vue/es')['Badge']
    ABreadcrumb: typeof import('ant-design-vue/es')['Breadcrumb']
    ABreadcrumbItem: typeof import('ant-design-vue/es')['BreadcrumbItem']
    AButton: typeof import('ant-design-vue/es')['Button']
    AButtonGroup: typeof import('ant-design-vue/es')['ButtonGroup']
    ACard: typeof import('ant-design-vue/es')['Card']
    ACol: typeof import('ant-design-vue/es')['Col']
    AConfigProvider: typeof import('ant-design-vue/es')['ConfigProvider']
    ADatePicker: typeof import('ant-design-vue/es')['DatePicker']
    ADescriptions: typeof import('ant-design-vue/es')['Descriptions']
    ADescriptionsItem: typeof import('ant-design-vue/es')['DescriptionsItem']
    ADirectoryTree: typeof import('ant-design-vue/es')['DirectoryTree']
    ADivider: typeof import('ant-design-vue/es')['Divider']
    ADrawer: typeof import('ant-design-vue/es')['Drawer']
    ADropdown: typeof import('ant-design-vue/es')['Dropdown']
    AEmpty: typeof import('ant-design-vue/es')['Empty']
    AForm: typeof import('ant-design-vue/es')['Form']
    AFormItem: typeof import('ant-design-vue/es')['FormItem']
    AInput: typeof import('ant-design-vue/es')['Input']
    AInputNumber: typeof import('ant-design-vue/es')['InputNumber']
    AInputPassword: typeof import('ant-design-vue/es')['InputPassword']
    AInputSearch: typeof import('ant-design-vue/es')['InputSearch']
    ALayout: typeof import('ant-design-vue/es')['Layout']
    ALayoutContent: typeof import('ant-design-vue/es')['LayoutContent']
    ALayoutFooter: typeof import('ant-design-vue/es')['LayoutFooter']
    ALayoutHeader: typeof import('ant-design-vue/es')['LayoutHeader']
    ALayoutSider: typeof import('ant-design-vue/es')['LayoutSider']
    AList: typeof import('ant-design-vue/es')['List']
    AListItem: typeof import('ant-design-vue/es')['ListItem']
    AListItemMeta: typeof import('ant-design-vue/es')['ListItemMeta']
    AMenu: typeof import('ant-design-vue/es')['Menu']
    AMenuItem: typeof import('ant-design-vue/es')['MenuItem']
    AModal: typeof import('ant-design-vue/es')['Modal']
    APagination: typeof import('ant-design-vue/es')['Pagination']
    APopover: typeof import('ant-design-vue/es')['Popover']
    ARadio: typeof import('ant-design-vue/es')['Radio']
    ARadioButton: typeof import('ant-design-vue/es')['RadioButton']
    ARadioGroup: typeof import('ant-design-vue/es')['RadioGroup']
    ARangePicker: typeof import('ant-design-vue/es')['RangePicker']
    AreaCascader: typeof import('./../src/components/framework/area-cascader/index.vue')['default']
    AResult: typeof import('ant-design-vue/es')['Result']
    ARow: typeof import('ant-design-vue/es')['Row']
    ASelect: typeof import('ant-design-vue/es')['Select']
    ASelectOption: typeof import('ant-design-vue/es')['SelectOption']
    ASlider: typeof import('ant-design-vue/es')['Slider']
    ASpace: typeof import('ant-design-vue/es')['Space']
    ASpin: typeof import('ant-design-vue/es')['Spin']
    ASubMenu: typeof import('ant-design-vue/es')['SubMenu']
    ASwitch: typeof import('ant-design-vue/es')['Switch']
    ATable: typeof import('ant-design-vue/es')['Table']
    ATableColumn: typeof import('ant-design-vue/es')['TableColumn']
    ATabPane: typeof import('ant-design-vue/es')['TabPane']
    ATabs: typeof import('ant-design-vue/es')['Tabs']
    ATag: typeof import('ant-design-vue/es')['Tag']
    ATextarea: typeof import('ant-design-vue/es')['Textarea']
    ATooltip: typeof import('ant-design-vue/es')['Tooltip']
    ATreeSelect: typeof import('ant-design-vue/es')['TreeSelect']
    AUpload: typeof import('ant-design-vue/es')['Upload']
    AxBoxSelect: typeof import('./../src/components/business/ax-box-select/index.vue')['default']
    AxDescriptionItem: typeof import('./../src/components/framework/ax-description-item/index.vue')['default']
    AxEntityTypeSelect: typeof import('./../src/components/business/ax-entity-type-select/index.vue')['default']
    AxImportExcelModal: typeof import('./../src/components/framework/ax-import-excel-modal/index.vue')['default']
    AxPagination: typeof import('./../src/components/framework/ax-pagination/index.vue')['default']
    AxParamTypeSelect: typeof import('./../src/components/business/ax-param-type-select/index.vue')['default']
    AxSearchModal: typeof import('./../src/components/framework-new/AxSearchSelect/AxSearchModal.vue')['default']
    AxSearchSelect: typeof import('./../src/components/framework-new/AxSearchSelect/AxSearchSelect.vue')['default']
    AxSupplierSelect: typeof import('./../src/components/business/ax-supplier-select/index.vue')['default']
    AxTable: typeof import('./../src/components/framework/ax-table/index.vue')['default']
    AxTableExpand: typeof import('./../src/components/framework/ax-table-expand/index.vue')['default']
    AxTableOperateButtons: typeof import('./../src/components/framework/ax-table-operate-buttons/index.vue')['default']
    BooleanRadio: typeof import('./../src/components/framework/boolean-radio/index.vue')['default']
    BooleanSelect: typeof import('./../src/components/framework/boolean-select/index.vue')['default']
    BoxFormModal: typeof import('./../src/components/business/ax-box-select/box-form-modal.vue')['default']
    Button: typeof import('./../src/components/framework-new/Button/Button.vue')['default']
    ButtonGroup: typeof import('./../src/components/framework-new/Button/ButtonGroup.vue')['default']
    Card: typeof import('./../src/components/framework-new/Card/Card.vue')['default']
    CategoryTreeSelect: typeof import('./../src/components/business/category-tree-select/index.vue')['default']
    CheckSquareFilled: typeof import('@ant-design/icons-vue')['CheckSquareFilled']
    CloseOutlined: typeof import('@ant-design/icons-vue')['CloseOutlined']
    ColorPreview: typeof import('./../src/components/support-new/Color/ColorPreview.vue')['default']
    CustomerSelect: typeof import('./../src/components/business/customer-select/index.vue')['default']
    CustomIcon: typeof import('./../src/components/framework-new/CustomIcon/CustomIcon.vue')['default']
    CustomIconAnchor: typeof import('./../src/components/framework-new/CustomIcon/CustomIconAnchor.vue')['default']
    CustomIconButton: typeof import('./../src/components/framework-new/CustomIcon/CustomIconButton.vue')['default']
    CustomText: typeof import('./../src/components/framework-new/CustomText/CustomText.vue')['default']
    DataTracer: typeof import('./../src/components/support/data-tracer/index.vue')['default']
    DataTracerTable: typeof import('./../src/components/support/data-tracer/data-tracer-table.vue')['default']
    DataTracerTimeline: typeof import('./../src/components/support/data-tracer/data-tracer-timeline.vue')['default']
    DatePicker: typeof import('./../src/components/framework-new/Picker/DatePicker.vue')['default']
    DateRangePicker: typeof import('./../src/components/framework-new/Picker/DateRangePicker.vue')['default']
    DeleteOutlined: typeof import('@ant-design/icons-vue')['DeleteOutlined']
    DepartmentTreeSelect: typeof import('./../src/components/system/department-tree-select/index.vue')['default']
    Descriptions: typeof import('./../src/components/framework-new/Descriptions/Descriptions.vue')['default']
    DescriptionsItem: typeof import('./../src/components/framework-new/Descriptions/DescriptionsItem.vue')['default']
    DictKeySelect: typeof import('./../src/components/support/dict-key-select/index.vue')['default']
    DictRadio: typeof import('./../src/components/support/dict-radio/index.vue')['default']
    DictSelect: typeof import('./../src/components/support/dict-select/index.vue')['default']
    DictTag: typeof import('./../src/components/support/dict-tag/index.vue')['default']
    DictValue: typeof import('./../src/components/support/dict-value/index.vue')['default']
    DragOutlined: typeof import('@ant-design/icons-vue')['DragOutlined']
    EmployeeSelect: typeof import('./../src/components/system/employee-select/index.vue')['default']
    EmployeeTableSelectModal: typeof import('./../src/components/system/employee-table-select-modal/index.vue')['default']
    EnterpriseBankSelect: typeof import('./../src/components/business/oa/enterprise-bank-select/index.vue')['default']
    EnterpriseInvoiceSelect: typeof import('./../src/components/business/oa/enterprise-invoice-select/index.vue')['default']
    EnterpriseSelect: typeof import('./../src/components/business/oa/enterprise-select/index.vue')['default']
    FilePreview: typeof import('./../src/components/support/file-preview/index.vue')['default']
    FilePreviewModal: typeof import('./../src/components/support/file-preview-modal/index.vue')['default']
    FileUpload: typeof import('./../src/components/support/file-upload/index.vue')['default']
    FlexRow: typeof import('./../src/components/framework-new/Flex/FlexRow.vue')['default']
    Form: typeof import('./../src/components/framework-new/Form/Form.vue')['default']
    FormColumn: typeof import('./../src/components/framework-new/Form/FormColumn.vue')['default']
    FormItem: typeof import('./../src/components/framework-new/Form/FormItem.vue')['default']
    FormRow: typeof import('./../src/components/framework-new/Form/FormRow.vue')['default']
    FullscreenExitOutlined: typeof import('@ant-design/icons-vue')['FullscreenExitOutlined']
    FullscreenOutlined: typeof import('@ant-design/icons-vue')['FullscreenOutlined']
    HomeOutlined: typeof import('@ant-design/icons-vue')['HomeOutlined']
    IconSelect: typeof import('./../src/components/framework/icon-select/index.vue')['default']
    IframeIndex: typeof import('./../src/components/framework/iframe/iframe-index.vue')['default']
    InputNumber: typeof import('./../src/components/framework-new/Input/InputNumber.vue')['default']
    InputText: typeof import('./../src/components/framework-new/Input/InputText.vue')['default']
    MenuFoldOutlined: typeof import('@ant-design/icons-vue')['MenuFoldOutlined']
    MenuTreeSelect: typeof import('./../src/components/system/menu-tree-select/index.vue')['default']
    MenuUnfoldOutlined: typeof import('@ant-design/icons-vue')['MenuUnfoldOutlined']
    Modal: typeof import('./../src/components/framework-new/Modal/Modal.vue')['default']
    MoreOutlined: typeof import('@ant-design/icons-vue')['MoreOutlined']
    PhoneOutlined: typeof import('@ant-design/icons-vue')['PhoneOutlined']
    PlusOutlined: typeof import('@ant-design/icons-vue')['PlusOutlined']
    PositionSelect: typeof import('./../src/components/system/position-select/index.vue')['default']
    PrintingProcessMultiSelect: typeof import('./../src/components/business/printing-process-multi-select/index.vue')['default']
    PrintingProcessSelect: typeof import('./../src/components/business/printing-process-select/index.vue')['default']
    PushpinOutlined: typeof import('@ant-design/icons-vue')['PushpinOutlined']
    Radio: typeof import('./../src/components/framework-new/Radio/Radio.vue')['default']
    RadioGroup: typeof import('./../src/components/framework-new/Radio/RadioGroup.vue')['default']
    RedoOutlined: typeof import('@ant-design/icons-vue')['RedoOutlined']
    ReloadOutlined: typeof import('@ant-design/icons-vue')['ReloadOutlined']
    RouteDefaultComponent: typeof import('./../src/components/framework/iframe/route-default-component.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SearchOutlined: typeof import('@ant-design/icons-vue')['SearchOutlined']
    SettingOutlined: typeof import('@ant-design/icons-vue')['SettingOutlined']
    SmartEnumCheckbox: typeof import('./../src/components/framework/smart-enum-checkbox/index.vue')['default']
    SmartEnumRadio: typeof import('./../src/components/framework/smart-enum-radio/index.vue')['default']
    SmartEnumSelect: typeof import('./../src/components/framework/smart-enum-select/index.vue')['default']
    SmartTableColumnModal: typeof import('./../src/components/support/table-operator/smart-table-column-modal.vue')['default']
    SmileOutlined: typeof import('@ant-design/icons-vue')['SmileOutlined']
    Spin: typeof import('./../src/components/framework-new/Spin/Spin.vue')['default']
    SupplierModal: typeof import('./../src/components/business/ax-supplier-select/supplier-modal.vue')['default']
    SupplierPaperSelect: typeof import('./../src/components/business/supplier-paper-select/index.vue')['default']
    SupplierSelect: typeof import('./../src/components/business/supplier-select/index.vue')['default']
    TableColumnManagerModal: typeof import('./../src/components/support-new/TableOperator/TableColumnManagerModal.vue')['default']
    TableOperator: typeof import('./../src/components/support/table-operator/index.vue')['default']
    Test: typeof import('./../src/components/support/dict-tag/test.vue')['default']
    TimePicker: typeof import('./../src/components/framework-new/Picker/TimePicker.vue')['default']
    TitleForm: typeof import('./../src/components/support/title-form/index.vue')['default']
    UploadOutlined: typeof import('@ant-design/icons-vue')['UploadOutlined']
    Wangeditor: typeof import('./../src/components/framework/wangeditor/index.vue')['default']
    WarehouseSelect: typeof import('./../src/components/business/warehouse-select/index.vue')['default']
    WarehouseSelector: typeof import('./../src/components/business/warehouse-selector/index.vue')['default']
  }
}
