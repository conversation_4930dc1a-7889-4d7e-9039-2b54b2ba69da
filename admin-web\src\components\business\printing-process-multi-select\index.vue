<script setup lang="ts">
import type { PrintingProcessModel } from '@/api/business/printing-process/model/printing-process-model'
import PrintingProcessSelect from '@/components/business/printing-process-select/index.vue'
import { theme } from 'ant-design-vue'
import { nextTick, onMounted, ref, watch } from 'vue'

const props = defineProps({
  modelValue: {
    type: Array as () => PrintingProcessModel[],
    default: () => [],
  },
  maxCount: {
    type: Number,
    default: 10,
  },
  minCount: {
    type: Number,
    default: 1,
  },
})
const emit = defineEmits(['update:modelValue', 'change'])
const { useToken } = theme
const { token } = useToken()

interface EmptyProcess {
  id: null
}

type ProcessListItem = PrintingProcessModel | EmptyProcess

// 检查对象是否是PrintingProcessModel
function isPrintingProcessModel(obj: ProcessListItem): obj is PrintingProcessModel {
  return obj.id !== null
}

// 定义印刷工艺选择列表
const innerPrintingProcessList = ref<PrintingProcessModel[]>(
  props.modelValue?.length
    ? [...props.modelValue]
    : [],
)

// 防止递归更新的标志
const isUpdating = ref(false)

// 更新modelValue，不触发其他操作
function updateModelValue() {
  // 只保存有值的数据到表单
  const validProcesses = innerPrintingProcessList.value
    .filter(isPrintingProcessModel)

  emit('update:modelValue', validProcesses)
  emit('change', validProcesses)
}

// 处理选择器列表，确保格式正确
function normalizeProcessList() {
  if (isUpdating.value)
    return

  isUpdating.value = true

  try {
    const list = innerPrintingProcessList.value
    // 确保不超过最大数量
    if (list.length > props.maxCount) {
      list.splice(props.maxCount)
    }

    // 确保至少有一个选择器
    if (list.length === 0) {
      list.push({ id: undefined })
    }

    // 收集所有非空项
    const nonEmptyItems = list.filter(isPrintingProcessModel)

    // 重建列表：先放入所有非空项，再确保末尾有一个空项
    if (nonEmptyItems.length < props.maxCount) {
      // 只保留一个空项在末尾
      innerPrintingProcessList.value = [
        ...nonEmptyItems,
        { id: undefined },
      ]
    }
    else {
      // 已经达到最大数量，不添加空项
      innerPrintingProcessList.value = [...nonEmptyItems]
    }

    // 更新表单数据
    updateModelValue()
  }
  finally {
    // 使用nextTick确保DOM更新后再清除标志
    nextTick(() => {
      isUpdating.value = false
    })
  }
}

// 使用watch观察值变化，但不在watch中修改值
watch(innerPrintingProcessList, () => {
  if (!isUpdating.value) {
    updateModelValue()
  }
}, { deep: true })

// 监听props.modelValue变化
watch(() => props.modelValue, (newVal) => {
  if (isUpdating.value)
    return

  // 如果外部值变化且与内部值不同，则更新内部值
  const currentValues = innerPrintingProcessList.value
    .filter(isPrintingProcessModel)
    .map(item => item.id)

  const newValues = newVal.map(item => item.id)

  if (JSON.stringify(currentValues) !== JSON.stringify(newValues)) {
    isUpdating.value = true
    nextTick(() => {
      innerPrintingProcessList.value = [
        ...newVal,
        { id: undefined },
      ]
      nextTick(() => {
        isUpdating.value = false
        updateModelValue()
      })
    })
  }
}, { deep: true })

// 监听每个选择器的值变化
function handleProcessChange(process: ProcessListItem, index: number) {
  if (isUpdating.value)
    return

  // 如果最后一个选择器被选中值，且未达到最大数量
  if (isPrintingProcessModel(process)
    && index === innerPrintingProcessList.value.length - 1
    && innerPrintingProcessList.value.length < props.maxCount) {
    // 在列表最后添加一个空选择器
    innerPrintingProcessList.value.push({ id: undefined })
    updateModelValue()
    return
  }

  // 如果选择器被清空且不是最后一个
  if (!isPrintingProcessModel(process) && index < innerPrintingProcessList.value.length - 1) {
    // 移除该选择器
    innerPrintingProcessList.value.splice(index, 1)
    updateModelValue()
    return
  }

  // 其他情况下仅更新表单数据
  updateModelValue()
}

function handleDelProcessItem(process: ProcessListItem) {
  if (isUpdating.value)
    return

  if (innerPrintingProcessList.value.length <= props.minCount) {
    return
  }

  const index = innerPrintingProcessList.value.indexOf(process)
  if (index !== -1) {
    // 删除当前项
    innerPrintingProcessList.value.splice(index, 1)

    // 如果删除后没有空项，添加一个空项在末尾
    if (innerPrintingProcessList.value.every(isPrintingProcessModel)
      && innerPrintingProcessList.value.length < props.maxCount) {
      innerPrintingProcessList.value.push({ id: undefined })
    }

    updateModelValue()
  }
}

// 处理印刷工艺选择事件
function handleProcessSelect(selectedProcess: PrintingProcessModel, _process: ProcessListItem, index: number) {
  // 使用完整的PrintingProcessModel对象替换现有对象
  innerPrintingProcessList.value[index] = selectedProcess
  handleProcessChange(selectedProcess, index)
}

// 处理印刷工艺清除事件
function handleProcessClear(_process: ProcessListItem, index: number) {
  innerPrintingProcessList.value[index] = { id: undefined }
  handleProcessChange({ id: undefined }, index)
}

// 组件挂载后初始化一次列表，确保格式正确
onMounted(() => {
  nextTick(normalizeProcessList)
})
</script>

<template>
  <div class="printing-process-multi-select">
    <div
      v-for="(process, index) in innerPrintingProcessList"
      :key="index"
      class="w-full flex items-center mb-2"
    >
      <PrintingProcessSelect
        :model-value="process.id !== null ? Number(process.id) : undefined"
        class="flex-1"
        placeholder="请选择印刷工艺"
        @select="selectedProcess => handleProcessSelect(selectedProcess, process, index)"
        @clear="() => handleProcessClear(process, index)"
        @update:model-value="newValue => {
          const currentList = [...innerPrintingProcessList];
          if (newValue !== undefined) {
            // 这里仅临时更新ID，保持UI响应性
            currentList[index] = { ...process, id: newValue };
          }
          else {
            currentList[index] = { id: undefined };
          }
          innerPrintingProcessList = currentList;
          handleProcessChange(innerPrintingProcessList[index], index);
        }"
      />
      <i
        v-if="!isUpdating && (index < innerPrintingProcessList.length - 1 || (innerPrintingProcessList.length > minCount && process.id !== null))"
        class="i-ant-design:minus-circle-outlined text-28px ml-8px "
        :style="{ color: token.colorError }"
        @click="handleDelProcessItem(process)"
      />
    </div>
  </div>
</template>

<style scoped>
.printing-process-multi-select {
  width: 100%;
}
</style>
