---
description: 
globs: 
alwaysApply: false
---
让你写SQL或者编辑SQL的时候，都要遵循以下规范：每一条都要遵守



0.设计之前阅读.sql\tables\table-relation.md 了解已设计的表关系,如果有必要阅读可能关联的完整表信息,用搜索工具搜 table_name.sql
1.基本规范：
   - 表的前缀统一使用erp_,使用下划线命名法，如erp_supplier
   - 建表语句不要加上数据库名
   - 表的备注不要写"表"字，例如应写"纸箱基础信息"而非"纸箱基础信息表"
   - 建表完加上最简单的索引确保基础性能
   - 主键一般命名为id

2.PostgreSQL特定规范：
   - 使用BIGSERIAL类型作为自增主键，而不是AUTO_INCREMENT
   - 在每个字段定义右侧添加`-- 字段中文含义`形式的注释，方便阅读
   - 数据库正式注释使用COMMENT ON语法单独添加
   - 使用SMALLINT替代TINYINT类型
   - 约束和索引定义建议与表分开编写

3.字段和注释规范：
   - 数据字典类字段固定varchar(20)
   - 数据字典类字段正式注释格式为：'字典 | 字段中文名 字段名：值1-含义1，值2-含义2'
   - 只有0和1的字段不要用字典，使用SMALLINT类型
   - 如果数据库的字段包含其他表的虚拟外键，正式注释格式为：'字段中文名 | 关联表名的id'
   - 所有id的数据类型都是bigint
   - 不要创建外键约束，会影响性能

4.PostgreSQL表结构示例：
```sql
-- 表创建
CREATE TABLE erp_equipment_demo (
  id BIGSERIAL PRIMARY KEY,  -- 设备ID
  equipment_code VARCHAR(50) NOT NULL,  -- 设备编码
  equipment_type VARCHAR(20) DEFAULT NULL,  -- 设备类型
  deleted SMALLINT DEFAULT 0,  -- 删除标志：0-未删除，1-已删除
  CONSTRAINT uk_equipment_code UNIQUE (equipment_code)  -- 设备编码唯一约束
);

-- 索引创建
CREATE INDEX idx_equipment_type ON erp_equipment_demo (equipment_type);  -- 设备类型索引

-- 正式注释添加
COMMENT ON TABLE erp_equipment_demo IS '设备演示信息';
COMMENT ON COLUMN erp_equipment_demo.id IS '设备ID';
COMMENT ON COLUMN erp_equipment_demo.equipment_code IS '设备编码';
COMMENT ON COLUMN erp_equipment_demo.equipment_type IS '字典 | 设备类型 equipment_type：production-生产设备，inspection-检测设备，office-办公设备';
COMMENT ON COLUMN erp_equipment_demo.deleted IS '删除标志：0-未删除，1-已删除';
```